<?php
/**
 * File Upload Handler for Post-Activity Report System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class FileUploadHandler {
    private $db;
    private $uploadDir = '../uploads/';
    private $maxFileSize = 10485760; // 10MB
    private $allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif'
    ];

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Create upload directory if it doesn't exist
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }

    public function handleUpload() {
        try {
            if (!isset($_FILES['file'])) {
                throw new Exception('No file uploaded');
            }

            $file = $_FILES['file'];
            $reportId = $_POST['reportId'] ?? null;

            // Validate file
            $this->validateFile($file);

            // Create new report if needed
            if (!$reportId) {
                $reportId = $this->createNewReport();
            }

            // Process and store file
            $fileId = $this->storeFile($file, $reportId);

            // Start background processing
            $this->processFileAsync($fileId);

            return [
                'success' => true,
                'message' => 'File uploaded successfully',
                'fileId' => $fileId,
                'reportId' => $reportId
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error: ' . $this->getUploadErrorMessage($file['error']));
        }

        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            throw new Exception('File too large. Maximum size is 10MB.');
        }

        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $this->allowedTypes)) {
            throw new Exception('File type not allowed: ' . $mimeType);
        }
    }

    private function createNewReport() {
        $query = "INSERT INTO activity_reports (created_at) VALUES (NOW())";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $reportId = $this->db->lastInsertId();
        
        // Create default sector categories
        $this->createDefaultSectorCategories($reportId);
        
        return $reportId;
    }

    private function createDefaultSectorCategories($reportId) {
        $sectors = ['NGA', 'LGU', 'SUC', 'Others'];
        
        foreach ($sectors as $sector) {
            $query = "INSERT INTO sector_categories (report_id, sector_name, total_count, male_count, female_count) 
                      VALUES (?, ?, 0, 0, 0)";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$reportId, $sector]);
        }
    }

    private function storeFile($file, $reportId) {
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = $this->uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Failed to save uploaded file');
        }

        // Store file info in database
        $query = "INSERT INTO uploaded_files 
                  (report_id, original_filename, stored_filename, file_path, file_type, file_size, upload_status) 
                  VALUES (?, ?, ?, ?, ?, ?, 'uploaded')";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            $reportId,
            $file['name'],
            $filename,
            $filepath,
            $file['type'],
            $file['size']
        ]);

        return $this->db->lastInsertId();
    }

    private function processFileAsync($fileId) {
        // Update status to processing
        $query = "UPDATE uploaded_files SET upload_status = 'processing' WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$fileId]);

        // In a real application, you might use a job queue here
        // For now, we'll process immediately
        $this->processFile($fileId);
    }

    private function processFile($fileId) {
        try {
            // Get file info
            $query = "SELECT * FROM uploaded_files WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$fileId]);
            $file = $stmt->fetch();

            if (!$file) {
                throw new Exception('File not found');
            }

            // Extract text based on file type
            $extractedText = $this->extractText($file['file_path'], $file['file_type']);

            // Parse extracted data
            $parsedData = $this->parseExtractedData($extractedText);

            // Store extracted data
            $this->storeExtractedData($fileId, $parsedData);

            // Update file status
            $query = "UPDATE uploaded_files SET upload_status = 'processed' WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$fileId]);

        } catch (Exception $e) {
            // Update file status to failed
            $query = "UPDATE uploaded_files SET upload_status = 'failed' WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$fileId]);
            
            error_log("File processing failed for file ID $fileId: " . $e->getMessage());
        }
    }

    private function extractText($filePath, $fileType) {
        switch ($fileType) {
            case 'application/pdf':
                return $this->extractFromPDF($filePath);
            
            case 'application/msword':
                return $this->extractFromDOC($filePath);
            
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return $this->extractFromDOCX($filePath);
            
            case 'text/plain':
                return file_get_contents($filePath);
            
            default:
                if (strpos($fileType, 'image/') === 0) {
                    return $this->extractFromImage($filePath);
                }
                return '';
        }
    }

    private function extractFromPDF($filePath) {
        // Try using pdftotext if available
        if (shell_exec('which pdftotext')) {
            $output = shell_exec("pdftotext '$filePath' -");
            if ($output) return $output;
        }
        
        // Fallback: basic PDF text extraction (limited)
        $content = file_get_contents($filePath);
        if (preg_match_all('/\((.*?)\)/', $content, $matches)) {
            return implode(' ', $matches[1]);
        }
        
        return '';
    }

    private function extractFromDOC($filePath) {
        // Try using antiword if available
        if (shell_exec('which antiword')) {
            $output = shell_exec("antiword '$filePath'");
            if ($output) return $output;
        }
        
        return '';
    }

    private function extractFromDOCX($filePath) {
        $zip = new ZipArchive();
        if ($zip->open($filePath) === TRUE) {
            $xml = $zip->getFromName('word/document.xml');
            $zip->close();
            
            if ($xml) {
                // Remove XML tags and extract text
                $text = strip_tags($xml);
                return html_entity_decode($text);
            }
        }
        
        return '';
    }

    private function extractFromImage($filePath) {
        // For images, we'll extract metadata and return filename info
        // In a real application, you might use OCR here
        $info = pathinfo($filePath);
        return "Image file: " . $info['filename'];
    }

    private function parseExtractedData($text) {
        $data = [];
        
        // Define patterns to extract different types of information
        $patterns = [
            'course_title' => [
                '/course\s*title[:\s]+([^\n\r]+)/i',
                '/training\s*title[:\s]+([^\n\r]+)/i',
                '/title[:\s]+([^\n\r]+)/i'
            ],
            'course_code' => [
                '/course\s*code[:\s]+([^\n\r]+)/i',
                '/code[:\s]+([A-Z0-9\-]+)/i'
            ],
            'venue' => [
                '/venue[:\s]+([^\n\r]+)/i',
                '/location[:\s]+([^\n\r]+)/i',
                '/held\s*at[:\s]+([^\n\r]+)/i'
            ],
            'resource_person' => [
                '/resource\s*person[:\s]+([^\n\r]+)/i',
                '/facilitator[:\s]+([^\n\r]+)/i',
                '/instructor[:\s]+([^\n\r]+)/i'
            ],
            'duration' => [
                '/duration[:\s]+([^\n\r]+)/i',
                '/(\d+)\s*days?/i',
                '/(\d+)\s*hours?/i'
            ],
            'participants' => [
                '/participants?[:\s]+([^\n\r]+)/i',
                '/attendees?[:\s]+([^\n\r]+)/i'
            ]
        ];

        foreach ($patterns as $key => $patternList) {
            foreach ($patternList as $pattern) {
                if (preg_match($pattern, $text, $matches)) {
                    $data[$key] = trim($matches[1]);
                    break;
                }
            }
        }

        return $data;
    }

    private function storeExtractedData($fileId, $data) {
        foreach ($data as $type => $value) {
            if (!empty($value)) {
                $query = "INSERT INTO extracted_data (file_id, data_type, extracted_value, confidence_score) 
                          VALUES (?, ?, ?, 0.8)";
                $stmt = $this->db->prepare($query);
                $stmt->execute([$fileId, $type, $value]);
            }
        }
    }

    private function getUploadErrorMessage($errorCode) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        return $errors[$errorCode] ?? 'Unknown upload error';
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $handler = new FileUploadHandler();
    $result = $handler->handleUpload();
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
