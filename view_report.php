<?php
/**
 * Report Viewer - Displays generated reports
 */

require_once 'config/database.php';
require_once 'includes/report_template.php';

$reportId = $_GET['id'] ?? null;

if (!$reportId) {
    die('Report ID is required');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if report exists
    $query = "SELECT * FROM activity_reports WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$reportId]);
    $report = $stmt->fetch();
    
    if (!$report) {
        die('Report not found');
    }
    
    // Generate the report HTML
    $template = new ReportTemplate();
    $reportHTML = $template->generateReport($reportId);
    
    // Check if this is a download request
    if (isset($_GET['download']) && $_GET['download'] === 'pdf') {
        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="after_training_report_' . $reportId . '.pdf"');
        
        // Try to find existing PDF
        $pdfQuery = "SELECT generated_pdf_path FROM report_generation_log WHERE report_id = ?";
        $pdfStmt = $db->prepare($pdfQuery);
        $pdfStmt->execute([$reportId]);
        $pdfData = $pdfStmt->fetch();
        
        if ($pdfData && file_exists($pdfData['generated_pdf_path'])) {
            readfile($pdfData['generated_pdf_path']);
        } else {
            // Generate PDF on the fly (basic implementation)
            header('Content-Type: text/html');
            echo $reportHTML;
        }
        exit;
    }
    
    // Display the report
    echo $reportHTML;
    
} catch (Exception $e) {
    die('Error loading report: ' . $e->getMessage());
}
?>
