/**
 * Edit Report JavaScript - <PERSON>les report data editing interface
 */

class ReportEditor {
    constructor() {
        this.reportId = null;
        this.originalData = {};
        this.hasUnsavedChanges = false;
        
        this.init();
    }
    
    init() {
        this.reportId = this.getReportIdFromURL();
        if (!this.reportId) {
            this.showAlert('No report ID provided', 'danger');
            return;
        }
        
        this.setupEventListeners();
        this.loadReportData();
        this.setupAutoSave();
        this.setupKeyboardShortcuts();
    }
    
    getReportIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }
    
    setupEventListeners() {
        const form = document.getElementById('editReportForm');
        const previewBtn = document.getElementById('previewBtn');
        
        // Form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveChanges();
        });
        
        // Preview button
        previewBtn.addEventListener('click', () => {
            this.previewReport();
        });
        
        // Track changes
        form.addEventListener('input', () => {
            this.hasUnsavedChanges = true;
            this.updateSaveButtonState();
        });
        
        // Auto-calculate totals
        const maleInput = document.getElementById('maleAttendees');
        const femaleInput = document.getElementById('femaleAttendees');
        const totalInput = document.getElementById('totalAttendees');
        
        [maleInput, femaleInput].forEach(input => {
            input.addEventListener('input', () => {
                this.calculateTotalAttendees();
            });
        });
        
        // Warn about unsaved changes
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            }
        });
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        this.saveChanges();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.previewReport();
                        break;
                }
            } else if (e.key === 'Escape') {
                history.back();
            }
        });
    }
    
    setupAutoSave() {
        // Auto-save every 2 minutes
        setInterval(() => {
            if (this.hasUnsavedChanges) {
                this.autoSave();
            }
        }, 120000);
    }
    
    async loadReportData() {
        try {
            const response = await fetch(`api/recent_reports.php?id=${this.reportId}`);
            const result = await response.json();
            
            if (result.success) {
                this.originalData = result.report;
                this.populateForm(result.report);
                this.updateReportTitle(result.report.course_title);
            } else {
                this.showAlert('Failed to load report data: ' + result.message, 'danger');
            }
        } catch (error) {
            console.error('Load error:', error);
            this.showAlert('Failed to load report data: ' + error.message, 'danger');
        }
    }
    
    populateForm(data) {
        // Set report ID
        document.getElementById('reportId').value = this.reportId;
        
        // Populate all form fields
        const fieldMapping = {
            'courseTitle': 'course_title',
            'courseCode': 'course_code',
            'trainingDate': 'training_date',
            'trainingTime': 'training_time',
            'duration': 'duration',
            'venue': 'venue',
            'resourcePerson': 'resource_person',
            'platformUsed': 'platform_used',
            'mode': 'mode',
            'targetParticipants': 'target_participants',
            'totalAttendees': 'total_attendees',
            'maleAttendees': 'male_attendees',
            'femaleAttendees': 'female_attendees',
            'rationale': 'rationale',
            'objectives': 'objectives',
            'topicsCovered': 'topics_covered',
            'issuesConcerns': 'issues_concerns',
            'recommendations': 'recommendations',
            'actionPlans': 'action_plans',
            'preparedBy': 'prepared_by',
            'preparedByDesignation': 'prepared_by_designation',
            'approvedBy': 'approved_by',
            'approvedByDesignation': 'approved_by_designation'
        };
        
        Object.entries(fieldMapping).forEach(([fieldId, dataKey]) => {
            const element = document.getElementById(fieldId);
            if (element && data[dataKey] !== undefined) {
                element.value = data[dataKey] || '';
            }
        });
        
        // Format date if needed
        if (data.training_date) {
            const dateElement = document.getElementById('trainingDate');
            if (dateElement) {
                // Convert date to YYYY-MM-DD format for input[type="date"]
                const date = new Date(data.training_date);
                if (!isNaN(date.getTime())) {
                    dateElement.value = date.toISOString().split('T')[0];
                }
            }
        }
        
        this.hasUnsavedChanges = false;
        this.updateSaveButtonState();
    }
    
    updateReportTitle(title) {
        const titleElement = document.getElementById('reportTitle');
        if (titleElement) {
            titleElement.textContent = title ? ` - ${title}` : '';
        }
    }
    
    calculateTotalAttendees() {
        const maleCount = parseInt(document.getElementById('maleAttendees').value) || 0;
        const femaleCount = parseInt(document.getElementById('femaleAttendees').value) || 0;
        const totalElement = document.getElementById('totalAttendees');
        
        const calculatedTotal = maleCount + femaleCount;
        if (calculatedTotal > 0) {
            totalElement.value = calculatedTotal;
        }
    }
    
    async saveChanges() {
        const form = document.getElementById('editReportForm');
        const formData = new FormData(form);
        
        // Convert FormData to JSON
        const data = {};
        formData.forEach((value, key) => {
            data[key] = value;
        });
        
        try {
            const response = await fetch('api/update_report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Changes saved successfully!', 'success');
                this.hasUnsavedChanges = false;
                this.updateSaveButtonState();
                this.originalData = { ...data };
            } else {
                this.showAlert('Failed to save changes: ' + result.message, 'danger');
            }
        } catch (error) {
            console.error('Save error:', error);
            this.showAlert('Failed to save changes: ' + error.message, 'danger');
        }
    }
    
    async autoSave() {
        try {
            await this.saveChanges();
            this.showAlert('Auto-saved', 'info', 2000);
        } catch (error) {
            console.error('Auto-save error:', error);
        }
    }
    
    previewReport() {
        if (this.hasUnsavedChanges) {
            if (confirm('You have unsaved changes. Save before previewing?')) {
                this.saveChanges().then(() => {
                    this.openPreview();
                });
            } else {
                this.openPreview();
            }
        } else {
            this.openPreview();
        }
    }
    
    openPreview() {
        const previewUrl = `view_report.php?id=${this.reportId}`;
        window.open(previewUrl, '_blank');
    }
    
    updateSaveButtonState() {
        const saveBtn = document.querySelector('button[type="submit"]');
        if (saveBtn) {
            if (this.hasUnsavedChanges) {
                saveBtn.classList.remove('btn-success');
                saveBtn.classList.add('btn-warning');
                saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes *';
            } else {
                saveBtn.classList.remove('btn-warning');
                saveBtn.classList.add('btn-success');
                saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes';
            }
        }
    }
    
    showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertDiv);
        
        // Auto-remove after specified duration
        if (duration > 0) {
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        }
    }
    
    validateForm() {
        const requiredFields = [
            'courseTitle',
            'trainingDate',
            'venue'
        ];
        
        let isValid = true;
        const errors = [];
        
        requiredFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element && !element.value.trim()) {
                isValid = false;
                errors.push(`${element.labels[0].textContent} is required`);
                element.classList.add('is-invalid');
            } else if (element) {
                element.classList.remove('is-invalid');
            }
        });
        
        // Validate participant numbers
        const total = parseInt(document.getElementById('totalAttendees').value) || 0;
        const male = parseInt(document.getElementById('maleAttendees').value) || 0;
        const female = parseInt(document.getElementById('femaleAttendees').value) || 0;
        
        if (total > 0 && (male + female) !== total) {
            errors.push('Male + Female attendees should equal Total attendees');
            isValid = false;
        }
        
        if (errors.length > 0) {
            this.showAlert('Please fix the following errors:<br>• ' + errors.join('<br>• '), 'danger');
        }
        
        return isValid;
    }
}

// Initialize the editor when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ReportEditor();
});
