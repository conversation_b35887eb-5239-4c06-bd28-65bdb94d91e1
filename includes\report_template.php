<?php
/**
 * DICT After Training Report Template Generator
 * Generates HTML reports matching the exact DICT template format
 */

require_once 'config/database.php';

class ReportTemplate {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    public function generateReport($reportId) {
        // Get report data
        $reportData = $this->getReportData($reportId);
        $sectorData = $this->getSectorData($reportId);
        $photos = $this->getPhotoData($reportId);
        
        // Generate HTML
        $html = $this->buildHTML($reportData, $sectorData, $photos);
        
        return $html;
    }
    
    private function getReportData($reportId) {
        $query = "SELECT * FROM activity_reports WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
        return $stmt->fetch();
    }
    
    private function getSectorData($reportId) {
        $query = "SELECT * FROM sector_categories WHERE report_id = ? ORDER BY sector_name";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
        return $stmt->fetchAll();
    }
    
    private function getPhotoData($reportId) {
        $query = "SELECT * FROM photo_documentation WHERE report_id = ? ORDER BY display_order, created_at";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
        return $stmt->fetchAll();
    }
    
    private function buildHTML($data, $sectors, $photos) {
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>After Training Report - DICT</title>
    <style>
        ' . $this->getReportCSS() . '
    </style>
</head>
<body>
    <div class="report-container">
        ' . $this->buildHeader() . '
        ' . $this->buildTrainingDetails($data, $sectors) . '
        ' . $this->buildRationale($data) . '
        ' . $this->buildObjectives($data) . '
        ' . $this->buildTopicsCovered($data) . '
        ' . $this->buildIssuesConcerns($data) . '
        ' . $this->buildRecommendations($data) . '
        ' . $this->buildActionPlans($data) . '
        ' . $this->buildPhotoDocumentation($photos) . '
        ' . $this->buildSignatures($data) . '
    </div>
</body>
</html>';
        
        return $html;
    }
    
    private function getReportCSS() {
        return '
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.2;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .report-container {
            max-width: 8.5in;
            margin: 0 auto;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 10px;
        }
        
        .dept-name {
            font-size: 10pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 20px;
        }
        
        .section {
            margin-bottom: 20px;
        }
        
        .section-title {
            background-color: #f0f0f0;
            padding: 5px 10px;
            font-weight: bold;
            border: 1px solid #000;
            margin-bottom: 0;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }
        
        .details-table td, .details-table th {
            border: 1px solid #000;
            padding: 5px;
            vertical-align: top;
        }
        
        .label-cell {
            background-color: #f8f8f8;
            font-weight: bold;
            width: 150px;
        }
        
        .content-cell {
            background-color: white;
        }
        
        .participants-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
            margin-top: 10px;
        }
        
        .participants-table td, .participants-table th {
            border: 1px solid #000;
            padding: 3px 5px;
            text-align: center;
        }
        
        .participants-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .text-content {
            padding: 10px;
            border: 1px solid #000;
            min-height: 60px;
            text-align: justify;
        }
        
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        
        .photo-item {
            text-align: center;
        }
        
        .photo-item img {
            max-width: 100%;
            height: 200px;
            object-fit: cover;
            border: 1px solid #ccc;
        }
        
        .photo-caption {
            font-size: 9pt;
            margin-top: 5px;
        }
        
        .signatures {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-block {
            text-align: center;
            width: 45%;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }
        
        @media print {
            body { margin: 0; padding: 10px; }
            .report-container { max-width: none; }
        }
        ';
    }
    
    private function buildHeader() {
        return '
        <div class="header">
            <div class="logo">
                <img src="assets/images/dict-logo.png" alt="DICT Logo" style="width: 80px; height: 80px;" onerror="this.style.display=\'none\'">
            </div>
            <div class="dept-name">REPUBLIC OF THE PHILIPPINES</div>
            <div class="dept-name">DEPARTMENT OF INFORMATION AND</div>
            <div class="dept-name">COMMUNICATIONS TECHNOLOGY</div>
            <div class="report-title">AFTER TRAINING REPORT</div>
        </div>';
    }
    
    private function buildTrainingDetails($data, $sectors) {
        $totalAttendees = $data['total_attendees'] ?: 0;
        $maleAttendees = $data['male_attendees'] ?: 0;
        $femaleAttendees = $data['female_attendees'] ?: 0;
        
        // Calculate sector totals
        $sectorRows = '';
        foreach ($sectors as $sector) {
            $sectorRows .= '<tr>
                <td>' . htmlspecialchars($sector['sector_name']) . ':</td>
                <td>' . $sector['total_count'] . '</td>
                <td>Male</td>
                <td>' . $sector['male_count'] . '</td>
                <td>Female</td>
                <td>' . $sector['female_count'] . '</td>
            </tr>';
        }
        
        return '
        <div class="section">
            <div class="section-title">I. TRAINING DETAILS</div>
            <table class="details-table">
                <tr>
                    <td class="label-cell">Course Title:</td>
                    <td class="content-cell" colspan="5">' . htmlspecialchars($data['course_title'] ?: '') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Course Code:</td>
                    <td class="content-cell" colspan="5">' . htmlspecialchars($data['course_code'] ?: '') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Date:</td>
                    <td class="content-cell" colspan="3">' . htmlspecialchars($data['training_date'] ?: '') . '</td>
                    <td class="label-cell">Time:</td>
                    <td class="content-cell">' . htmlspecialchars($data['training_time'] ?: '') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Duration:</td>
                    <td class="content-cell" colspan="5">' . htmlspecialchars($data['duration'] ?: '') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Venue:</td>
                    <td class="content-cell" colspan="5">' . htmlspecialchars($data['venue'] ?: '') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Resource Person:</td>
                    <td class="content-cell" colspan="5">' . htmlspecialchars($data['resource_person'] ?: '') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Platform Used:</td>
                    <td class="content-cell">' . htmlspecialchars($data['platform_used'] ?: 'Google Colab') . '</td>
                    <td class="label-cell">Mode:</td>
                    <td class="content-cell" colspan="3">' . htmlspecialchars($data['mode'] ?: 'Face-to-Face') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Target Participants:</td>
                    <td class="content-cell" colspan="5">' . htmlspecialchars($data['target_participants'] ?: 'DICT Personnel') . '</td>
                </tr>
                <tr>
                    <td class="label-cell">Total # of Attendees:<br><small>(with/without submitted Evaluation Form/Output)</small></td>
                    <td class="content-cell">' . $totalAttendees . '</td>
                    <td>Male</td>
                    <td>' . $maleAttendees . '</td>
                    <td>Female</td>
                    <td>' . $femaleAttendees . '</td>
                </tr>
                <tr>
                    <td colspan="6" style="text-align: center; font-weight: bold; padding: 8px;">Number of Beneficiaries with Sex Disaggregation</td>
                </tr>
            </table>
            
            <table class="participants-table">
                <tr>
                    <th rowspan="2">Sector Category:</th>
                    <th colspan="5">Breakdown</th>
                </tr>
                ' . $sectorRows . '
                <tr style="background-color: #f0f0f0;">
                    <td><strong>Total # of Issued Certificates:</strong><br><small>(with submitted Evaluation Form/Output)</small></td>
                    <td><strong>' . $totalAttendees . '</strong></td>
                    <td><strong>Male</strong></td>
                    <td><strong>' . $maleAttendees . '</strong></td>
                    <td><strong>Female</strong></td>
                    <td><strong>' . $femaleAttendees . '</strong></td>
                </tr>
            </table>
        </div>';
    }

    private function buildRationale($data) {
        return '
        <div class="section">
            <div class="section-title">II. RATIONALE</div>
            <div class="text-content">
                ' . nl2br(htmlspecialchars($data['rationale'] ?: 'This course will provide an overview of the training content and its applications.')) . '
            </div>
        </div>';
    }

    private function buildObjectives($data) {
        return '
        <div class="section">
            <div class="section-title">III. OBJECTIVES</div>
            <div class="text-content">
                <em>Upon completion of the course, participants will be able to learn:</em><br><br>
                ' . nl2br(htmlspecialchars($data['objectives'] ?: '• Understand the key concepts and principles
• Apply learned techniques in practical scenarios
• Demonstrate proficiency in the subject matter')) . '
            </div>
        </div>';
    }

    private function buildTopicsCovered($data) {
        return '
        <div class="section">
            <div class="section-title">IV. TOPICS COVERED</div>
            <div class="text-content">
                <strong>Topics Covered:</strong><br>
                ' . nl2br(htmlspecialchars($data['topics_covered'] ?: 'The course included comprehensive modules covering the essential topics and practical applications.')) . '
            </div>
        </div>';
    }

    private function buildIssuesConcerns($data) {
        return '
        <div class="section">
            <div class="section-title">V. ISSUES AND CONCERNS</div>
            <div class="text-content">
                ' . nl2br(htmlspecialchars($data['issues_concerns'] ?: 'While the course content was generally well-received, a few concerns were noted regarding logistics and technical aspects.')) . '
            </div>
        </div>';
    }

    private function buildRecommendations($data) {
        return '
        <div class="section">
            <div class="section-title">VI. RECOMMENDATION</div>
            <div class="text-content">
                ' . nl2br(htmlspecialchars($data['recommendations'] ?: '• Increase hands-on activities to incorporate more working exercises
• Conduct an Initial Skills Assessment to determine participants\' knowledge levels
• Streamline theoretical content and provide more time for practical applications
• Provide pre-session materials and distribute visual aids and reading materials')) . '
            </div>
        </div>';
    }

    private function buildActionPlans($data) {
        return '
        <div class="section">
            <div class="section-title">VII. PLANS AND ACTION ITEMS (NEXT STEPS)</div>
            <div class="text-content">
                ' . nl2br(htmlspecialchars($data['action_plans'] ?: 'The Program of Instruction for the training is targeted for participants with basic to no knowledge and non-technical people. Hence, the following action steps will be implemented:

• Recommend to the concerned division to streamline course content for general audiences
• Prepare pre-session materials and coordinate with the resource person to distribute preparatory materials in advance to provide participants with foundational knowledge')) . '
            </div>
        </div>';
    }

    private function buildPhotoDocumentation($photos) {
        $photoHtml = '';

        if (!empty($photos)) {
            $photoHtml = '<div class="photo-grid">';
            foreach ($photos as $photo) {
                $photoHtml .= '
                <div class="photo-item">
                    <img src="' . htmlspecialchars($photo['photo_path']) . '" alt="Training Photo">
                    <div class="photo-caption">' . htmlspecialchars($photo['photo_caption'] ?: $photo['photo_date']) . '</div>
                </div>';
            }
            $photoHtml .= '</div>';
        } else {
            $photoHtml = '
            <div class="photo-grid">
                <div class="photo-item">
                    <div style="height: 200px; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; background-color: #f5f5f5;">
                        <span style="color: #666;">Photo 1</span>
                    </div>
                    <div class="photo-caption">Day 1</div>
                </div>
                <div class="photo-item">
                    <div style="height: 200px; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; background-color: #f5f5f5;">
                        <span style="color: #666;">Photo 2</span>
                    </div>
                    <div class="photo-caption">Day 2</div>
                </div>
            </div>';
        }

        return '
        <div class="section">
            <div class="section-title">VIII. PHOTO DOCUMENTATION</div>
            ' . $photoHtml . '
        </div>';
    }

    private function buildSignatures($data) {
        return '
        <div class="signatures">
            <div class="signature-block">
                <div style="margin-bottom: 10px;"><strong>Prepared by:</strong></div>
                <div class="signature-line"></div>
                <div><strong>' . htmlspecialchars($data['prepared_by'] ?: 'COURSE OFFICER') . '</strong></div>
                <div><em>&lt;Designation&gt;</em></div>
                <div>' . htmlspecialchars($data['prepared_by_designation'] ?: 'Training Management Division') . '</div>
            </div>

            <div class="signature-block">
                <div style="margin-bottom: 10px;"><strong>Approved by:</strong></div>
                <div class="signature-line"></div>
                <div><strong>' . htmlspecialchars($data['approved_by'] ?: 'OIC CHIEF') . '</strong></div>
                <div><em>&lt;Designation&gt;</em></div>
                <div>' . htmlspecialchars($data['approved_by_designation'] ?: 'Training Management Division') . '</div>
            </div>
        </div>';
    }
}
?>
