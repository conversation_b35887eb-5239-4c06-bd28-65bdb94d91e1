/**
 * Post-Activity Report Generator - Upload Handler
 */

class ReportGenerator {
    constructor() {
        this.uploadedFiles = [];
        this.currentReportId = null;
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif'
        ];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadRecentReports();
        this.initializeProcessingButtons();
    }

    setupEventListeners() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const generateBtn = document.getElementById('generateBtn');
        const clearBtn = document.getElementById('clearBtn');

        // Drag and drop events
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // Button events
        generateBtn.addEventListener('click', () => this.generateReport());
        clearBtn.addEventListener('click', () => this.clearAll());

        // Click to upload
        dropZone.addEventListener('click', () => {
            fileInput.click();
        });
    }

    handleFiles(files) {
        const validFiles = Array.from(files).filter(file => this.validateFile(file));
        
        if (validFiles.length === 0) {
            this.showMessage('No valid files selected. Please check file types and sizes.', 'warning');
            return;
        }

        validFiles.forEach(file => {
            this.addFileToList(file);
            this.uploadFile(file);
        });

        this.updateUI();
    }

    validateFile(file) {
        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            this.showMessage(`File type not supported: ${file.name}`, 'warning');
            return false;
        }

        // Check file size
        if (file.size > this.maxFileSize) {
            this.showMessage(`File too large: ${file.name} (max 10MB)`, 'warning');
            return false;
        }

        return true;
    }

    addFileToList(file) {
        const fileObj = {
            id: Date.now() + Math.random(),
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'uploaded'
        };

        this.uploadedFiles.push(fileObj);
        this.renderFileList();
    }

    renderFileList() {
        const fileList = document.getElementById('fileList');
        const fileItems = document.getElementById('fileItems');
        
        if (this.uploadedFiles.length === 0) {
            fileList.style.display = 'none';
            return;
        }

        fileList.style.display = 'block';
        fileItems.innerHTML = '';

        this.uploadedFiles.forEach(fileObj => {
            const fileItem = this.createFileItem(fileObj);
            fileItems.appendChild(fileItem);
        });
    }

    createFileItem(fileObj) {
        const div = document.createElement('div');
        div.className = 'file-item fade-in';
        
        const iconClass = this.getFileIcon(fileObj.type);
        const iconColor = this.getFileIconClass(fileObj.type);
        const fileSize = this.formatFileSize(fileObj.size);
        const statusBadge = this.getStatusBadge(fileObj.status);

        div.innerHTML = `
            <div class="file-info">
                <div class="file-icon ${iconColor}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="file-details">
                    <h6>${fileObj.name}</h6>
                    <small>${fileSize}</small>
                </div>
            </div>
            <div class="file-status">
                ${statusBadge}
                <button class="btn btn-sm btn-outline-danger" onclick="reportGen.removeFile(${fileObj.id})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        return div;
    }

    getFileIcon(type) {
        if (type.includes('pdf')) return 'fas fa-file-pdf';
        if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';
        if (type.includes('text')) return 'fas fa-file-alt';
        if (type.includes('image')) return 'fas fa-image';
        return 'fas fa-file';
    }

    getFileIconClass(type) {
        if (type.includes('pdf')) return 'pdf';
        if (type.includes('word') || type.includes('document')) return 'doc';
        if (type.includes('text')) return 'txt';
        if (type.includes('image')) return 'img';
        return 'doc';
    }

    getStatusBadge(status) {
        const badges = {
            uploaded: '<span class="status-badge uploaded">Uploaded</span>',
            processing: '<span class="status-badge processing"><span class="spinner me-1"></span>Processing</span>',
            processed: '<span class="status-badge processed">Processed</span>',
            failed: '<span class="status-badge failed">Failed</span>'
        };
        return badges[status] || badges.uploaded;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async uploadFile(file) {
        const fileObj = this.uploadedFiles.find(f => f.file === file);
        if (!fileObj) return;

        fileObj.status = 'processing';
        this.renderFileList();

        const formData = new FormData();
        formData.append('file', file);
        formData.append('reportId', this.currentReportId || '');

        try {
            const response = await fetch('api/upload.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                fileObj.status = 'processed';
                fileObj.fileId = result.fileId;
                this.currentReportId = result.reportId;
                this.showMessage(`File uploaded successfully: ${file.name}`, 'success');
            } else {
                fileObj.status = 'failed';
                this.showMessage(`Upload failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            fileObj.status = 'failed';
            this.showMessage(`Upload error: ${error.message}`, 'danger');
        }

        this.renderFileList();
        this.updateUI();
    }

    removeFile(fileId) {
        this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId);
        this.renderFileList();
        this.updateUI();
    }

    updateUI() {
        const actionButtons = document.querySelector('.action-buttons');
        const generateBtn = document.getElementById('generateBtn');
        const processBtn = document.getElementById('processBtn');
        const clearBtn = document.getElementById('clearBtn');

        const uploadedFiles = this.uploadedFiles.filter(f => f.status === 'uploaded');
        const processedFiles = this.uploadedFiles.filter(f => f.status === 'processed');

        if (this.uploadedFiles.length > 0) {
            actionButtons.style.display = 'block';

            // Show process button if there are uploaded files
            if (processBtn && uploadedFiles.length > 0) {
                processBtn.style.display = 'inline-block';
                processBtn.disabled = false;
            } else if (processBtn) {
                processBtn.style.display = 'none';
            }

            // Enable generate button only after processing
            generateBtn.disabled = processedFiles.length === 0;
            if (processedFiles.length > 0) {
                generateBtn.classList.remove('btn-secondary');
                generateBtn.classList.add('btn-success');
            }

            if (clearBtn) {
                clearBtn.style.display = 'inline-block';
            }
        } else {
            actionButtons.style.display = 'none';
            if (processBtn) {
                processBtn.style.display = 'none';
            }
        }
    }

    async generateReport() {
        if (!this.currentReportId) {
            this.showMessage('No files processed yet. Please upload files first.', 'warning');
            return;
        }

        const generateBtn = document.getElementById('generateBtn');
        const originalText = generateBtn.innerHTML;
        
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<span class="spinner me-2"></span>Generating...';

        try {
            const response = await fetch('api/generate.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reportId: this.currentReportId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('Report generated successfully!', 'success');
                
                // Open the generated report
                if (result.reportUrl) {
                    window.open(result.reportUrl, '_blank');
                }
                
                this.loadRecentReports();
            } else {
                this.showMessage(`Generation failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            this.showMessage(`Generation error: ${error.message}`, 'danger');
        } finally {
            generateBtn.disabled = false;
            generateBtn.innerHTML = originalText;
        }
    }

    clearAll() {
        this.uploadedFiles = [];
        this.currentReportId = null;
        this.renderFileList();
        this.updateUI();
        document.getElementById('fileInput').value = '';
        this.showMessage('All files cleared.', 'info');
    }

    async loadRecentReports() {
        try {
            const response = await fetch('api/recent_reports.php');
            const result = await response.json();

            if (result.success) {
                this.renderRecentReports(result.reports);
            }
        } catch (error) {
            console.error('Error loading recent reports:', error);
        }
    }

    renderRecentReports(reports) {
        const container = document.getElementById('recentReports');
        
        if (!reports || reports.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No recent reports</p>';
            return;
        }

        container.innerHTML = reports.map(report => `
            <div class="report-item">
                <div class="report-info">
                    <h6>${report.course_title || 'Untitled Report'}</h6>
                    <small>${new Date(report.created_at).toLocaleDateString()} •
                           <span class="text-${this.getStatusColor(report.status)}">${report.status || 'Draft'}</span>
                    </small>
                </div>
                <div class="report-actions">
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="view_report.php?id=${report.id}" class="btn btn-outline-primary" target="_blank" title="View Report">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="edit_report.php?id=${report.id}" class="btn btn-outline-info" title="Edit Report">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-outline-danger" onclick="event.stopPropagation(); reportGen.deleteReport('${report.id}')" title="Delete Report">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getStatusColor(status) {
        const colors = {
            completed: 'success',
            processing: 'warning',
            failed: 'danger',
            draft: 'muted'
        };
        return colors[status] || 'muted';
    }

    showMessage(message, type = 'info') {
        const container = document.getElementById('statusMessages');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(alertDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    initializeProcessingButtons() {
        // Process Files button
        const processBtn = document.createElement('button');
        processBtn.id = 'processBtn';
        processBtn.className = 'btn btn-info me-2';
        processBtn.innerHTML = '<i class="fas fa-cogs"></i> Process Files';
        processBtn.style.display = 'none';
        processBtn.addEventListener('click', () => this.processFiles());

        // Generate Report button
        const generateBtn = document.getElementById('generateBtn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateReport());
        }

        // Insert process button before generate button
        if (generateBtn && generateBtn.parentNode) {
            generateBtn.parentNode.insertBefore(processBtn, generateBtn);
        }
    }

    async processFiles() {
        if (!this.currentReportId) {
            this.showAlert('No files uploaded yet', 'warning');
            return;
        }

        const processBtn = document.getElementById('processBtn');
        const originalText = processBtn.innerHTML;

        try {
            processBtn.disabled = true;
            processBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

            const response = await fetch('api/process.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reportId: this.currentReportId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Files processed successfully! Extracted data is ready for report generation.', 'success');

                // Show extracted data preview
                if (result.extractedData) {
                    this.showExtractedDataPreview(result.extractedData);
                }

                // Enable generate button
                const generateBtn = document.getElementById('generateBtn');
                if (generateBtn) {
                    generateBtn.disabled = false;
                    generateBtn.classList.remove('btn-secondary');
                    generateBtn.classList.add('btn-success');
                }
            } else {
                this.showAlert('Processing failed: ' + result.message, 'danger');
            }
        } catch (error) {
            console.error('Processing error:', error);
            this.showAlert('Processing failed: ' + error.message, 'danger');
        } finally {
            processBtn.disabled = false;
            processBtn.innerHTML = originalText;
        }
    }

    async generateReport() {
        if (!this.currentReportId) {
            this.showAlert('No report to generate', 'warning');
            return;
        }

        const generateBtn = document.getElementById('generateBtn');
        const originalText = generateBtn.innerHTML;

        try {
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

            const response = await fetch('api/generate.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reportId: this.currentReportId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Report generated successfully!', 'success');

                // Show download options
                this.showDownloadOptions(result);

                // Refresh recent reports
                this.loadRecentReports();
            } else {
                if (result.fallback_html) {
                    this.showAlert('PDF generation failed, but HTML version is available for printing.', 'warning');
                    this.showDownloadOptions(result);
                } else {
                    this.showAlert('Report generation failed: ' + result.message, 'danger');
                }
            }
        } catch (error) {
            console.error('Generation error:', error);
            this.showAlert('Report generation failed: ' + error.message, 'danger');
        } finally {
            generateBtn.disabled = false;
            generateBtn.innerHTML = originalText;
        }
    }

    showExtractedDataPreview(data) {
        const previewHtml = `
            <div class="alert alert-info">
                <h6><i class="fas fa-eye"></i> Extracted Data Preview</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>Course Title:</strong> ${data.course_title || 'Not found'}</small><br>
                        <small><strong>Date:</strong> ${data.training_date || 'Not found'}</small><br>
                        <small><strong>Venue:</strong> ${data.venue || 'Not found'}</small><br>
                    </div>
                    <div class="col-md-6">
                        <small><strong>Participants:</strong> ${data.total_attendees || 'Not found'}</small><br>
                        <small><strong>Resource Person:</strong> ${data.resource_person || 'Not found'}</small><br>
                        <small><strong>Duration:</strong> ${data.duration || 'Not found'}</small><br>
                    </div>
                </div>
            </div>
        `;

        const alertContainer = document.getElementById('alertContainer');
        if (alertContainer) {
            const previewDiv = document.createElement('div');
            previewDiv.innerHTML = previewHtml;
            alertContainer.appendChild(previewDiv.firstElementChild);
        }
    }

    showDownloadOptions(result) {
        const downloadHtml = `
            <div class="alert alert-success">
                <h6><i class="fas fa-download"></i> Report Ready</h6>
                <div class="btn-group" role="group">
                    <a href="view_report.php?id=${this.currentReportId}" class="btn btn-primary btn-sm" target="_blank">
                        <i class="fas fa-eye"></i> View Report
                    </a>
                    ${result.pdfPath ? `
                        <a href="view_report.php?id=${this.currentReportId}&download=pdf" class="btn btn-success btn-sm">
                            <i class="fas fa-file-pdf"></i> Download PDF
                        </a>
                    ` : ''}
                    ${result.fallback_html ? `
                        <a href="${result.fallback_html}" class="btn btn-warning btn-sm" target="_blank">
                            <i class="fas fa-print"></i> Print Version
                        </a>
                    ` : ''}
                </div>
            </div>
        `;

        const alertContainer = document.getElementById('alertContainer');
        if (alertContainer) {
            const downloadDiv = document.createElement('div');
            downloadDiv.innerHTML = downloadHtml;
            alertContainer.appendChild(downloadDiv.firstElementChild);
        }
    }
}

// Initialize the application
const reportGen = new ReportGenerator();
