<?php
/**
 * Integration Testing API - Tests end-to-end system functionality
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../config/database.php';

class IntegrationTester {
    private $tests = [];
    private $db;
    private $testReportId = null;
    
    public function __construct() {
        try {
            $database = new Database();
            $this->db = $database->getConnection();
        } catch (Exception $e) {
            $this->db = null;
        }
    }
    
    public function runAllTests() {
        $this->testEndToEndWorkflow();
        $this->testFileUploadToReport();
        $this->testDataExtractionToDatabase();
        $this->testReportGeneration();
        $this->testSystemIntegration();
        
        // Cleanup test data
        $this->cleanup();
        
        $passed = count(array_filter($this->tests, function($test) { return $test['passed']; }));
        $total = count($this->tests);
        
        return [
            'success' => true,
            'tests' => $this->tests,
            'summary' => [
                'total' => $total,
                'passed' => $passed,
                'failed' => $total - $passed,
                'success_rate' => $total > 0 ? round(($passed / $total) * 100, 1) : 0
            ]
        ];
    }
    
    private function testEndToEndWorkflow() {
        try {
            if (!$this->db) {
                throw new Exception('Database connection required for integration tests');
            }
            
            // Step 1: Create a test report
            $stmt = $this->db->prepare("
                INSERT INTO activity_reports (
                    course_title, training_date, venue, total_participants, 
                    male_participants, female_participants, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                'Integration Test Course',
                '2024-01-15',
                'Test Venue',
                30,
                18,
                12
            ]);
            
            $this->testReportId = $this->db->lastInsertId();
            
            if (!$this->testReportId) {
                throw new Exception('Failed to create test report');
            }
            
            // Step 2: Add file record
            $stmt = $this->db->prepare("
                INSERT INTO uploaded_files (report_id, filename, file_path, file_type, file_size, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $this->testReportId,
                'test_document.txt',
                '/uploads/test_document.txt',
                'text/plain',
                1024
            ]);
            
            // Step 3: Add sector categories
            $stmt = $this->db->prepare("
                INSERT INTO sector_categories (report_id, sector_name, total_count, male_count, female_count)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $sectors = [
                ['Government', 15, 8, 7],
                ['Private', 10, 6, 4],
                ['Academic', 5, 4, 1]
            ];
            
            foreach ($sectors as $sector) {
                $stmt->execute(array_merge([$this->testReportId], $sector));
            }
            
            // Step 4: Add photo documentation
            $stmt = $this->db->prepare("
                INSERT INTO photo_documentation (report_id, image_path, caption, date_taken)
                VALUES (?, ?, ?, ?)
            ");
            
            $photos = [
                ['/uploads/photos/training1.jpg', 'Opening Ceremony', '2024-01-15 09:00:00'],
                ['/uploads/photos/training2.jpg', 'Group Discussion', '2024-01-15 14:00:00'],
                ['/uploads/photos/training3.jpg', 'Certificate Distribution', '2024-01-15 16:00:00']
            ];
            
            foreach ($photos as $photo) {
                $stmt->execute(array_merge([$this->testReportId], $photo));
            }
            
            // Step 5: Verify data integrity
            $stmt = $this->db->prepare("
                SELECT ar.*, 
                       COUNT(DISTINCT uf.id) as file_count,
                       COUNT(DISTINCT sc.id) as sector_count,
                       COUNT(DISTINCT pd.id) as photo_count
                FROM activity_reports ar
                LEFT JOIN uploaded_files uf ON ar.id = uf.report_id
                LEFT JOIN sector_categories sc ON ar.id = sc.report_id
                LEFT JOIN photo_documentation pd ON ar.id = pd.report_id
                WHERE ar.id = ?
                GROUP BY ar.id
            ");
            
            $stmt->execute([$this->testReportId]);
            $result = $stmt->fetch();
            
            if (!$result) {
                throw new Exception('Failed to retrieve test report data');
            }
            
            if ($result['file_count'] != 1 || $result['sector_count'] != 3 || $result['photo_count'] != 3) {
                throw new Exception('Data integrity check failed');
            }
            
            $this->addTest('End-to-End Workflow', 'Complete workflow from data creation to verification successful', true);
            
        } catch (Exception $e) {
            $this->addTest('End-to-End Workflow', 'End-to-end workflow test failed', false, $e->getMessage());
        }
    }
    
    private function testFileUploadToReport() {
        try {
            if (!$this->testReportId) {
                throw new Exception('Test report not available');
            }
            
            // Simulate file upload process
            $uploadDir = '../uploads/test/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Create test file
            $testContent = "Course: Integration Test\nDate: 2024-01-15\nVenue: Test Location\nParticipants: 30";
            $testFile = $uploadDir . 'integration_test.txt';
            file_put_contents($testFile, $testContent);
            
            // Simulate file processing
            $extractedText = file_get_contents($testFile);
            
            // Test data extraction patterns
            $patterns = [
                'course' => '/Course:\s*(.+?)(?:\n|$)/i',
                'date' => '/Date:\s*(.+?)(?:\n|$)/i',
                'venue' => '/Venue:\s*(.+?)(?:\n|$)/i',
                'participants' => '/Participants:\s*(\d+)/i'
            ];
            
            $extractedData = [];
            foreach ($patterns as $key => $pattern) {
                if (preg_match($pattern, $extractedText, $matches)) {
                    $extractedData[$key] = trim($matches[1]);
                }
            }
            
            // Verify extraction
            $expected = [
                'course' => 'Integration Test',
                'date' => '2024-01-15',
                'venue' => 'Test Location',
                'participants' => '30'
            ];
            
            foreach ($expected as $key => $value) {
                if (!isset($extractedData[$key]) || $extractedData[$key] !== $value) {
                    throw new Exception("Data extraction failed for $key: expected '$value', got '" . ($extractedData[$key] ?? 'null') . "'");
                }
            }
            
            // Cleanup
            unlink($testFile);
            rmdir($uploadDir);
            
            $this->addTest('File Upload to Report', 'File upload and data extraction integration successful', true);
            
        } catch (Exception $e) {
            $this->addTest('File Upload to Report', 'File upload integration test failed', false, $e->getMessage());
        }
    }
    
    private function testDataExtractionToDatabase() {
        try {
            if (!$this->testReportId) {
                throw new Exception('Test report not available');
            }
            
            // Test updating report with extracted data
            $extractedData = [
                'rationale' => 'To improve digital skills among participants',
                'objectives' => 'Learn modern web development techniques',
                'topics_covered' => 'HTML, CSS, JavaScript, PHP',
                'issues_concerns' => 'Limited time for hands-on practice',
                'recommendations' => 'Extend training duration',
                'action_plans' => 'Schedule follow-up sessions'
            ];
            
            $stmt = $this->db->prepare("
                UPDATE activity_reports 
                SET rationale = ?, objectives = ?, topics_covered = ?, 
                    issues_concerns = ?, recommendations = ?, action_plans = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                $extractedData['rationale'],
                $extractedData['objectives'],
                $extractedData['topics_covered'],
                $extractedData['issues_concerns'],
                $extractedData['recommendations'],
                $extractedData['action_plans'],
                $this->testReportId
            ]);
            
            // Verify update
            $stmt = $this->db->prepare("SELECT * FROM activity_reports WHERE id = ?");
            $stmt->execute([$this->testReportId]);
            $updated = $stmt->fetch();
            
            foreach ($extractedData as $key => $value) {
                if ($updated[$key] !== $value) {
                    throw new Exception("Database update failed for $key");
                }
            }
            
            $this->addTest('Data Extraction to Database', 'Extracted data successfully stored in database', true);
            
        } catch (Exception $e) {
            $this->addTest('Data Extraction to Database', 'Data extraction to database test failed', false, $e->getMessage());
        }
    }
    
    private function testReportGeneration() {
        try {
            if (!$this->testReportId) {
                throw new Exception('Test report not available');
            }
            
            // Fetch complete report data
            $stmt = $this->db->prepare("
                SELECT ar.*, 
                       GROUP_CONCAT(DISTINCT sc.sector_name) as sectors,
                       GROUP_CONCAT(DISTINCT pd.caption) as photo_captions
                FROM activity_reports ar
                LEFT JOIN sector_categories sc ON ar.id = sc.report_id
                LEFT JOIN photo_documentation pd ON ar.id = pd.report_id
                WHERE ar.id = ?
                GROUP BY ar.id
            ");
            
            $stmt->execute([$this->testReportId]);
            $reportData = $stmt->fetch();
            
            if (!$reportData) {
                throw new Exception('Failed to fetch report data');
            }
            
            // Generate HTML report
            $htmlReport = $this->generateHTMLReport($reportData);
            
            // Validate HTML structure
            if (strpos($htmlReport, 'DEPARTMENT OF INFORMATION AND COMMUNICATIONS TECHNOLOGY') === false) {
                throw new Exception('HTML report missing header');
            }
            
            if (strpos($htmlReport, $reportData['course_title']) === false) {
                throw new Exception('HTML report missing course title');
            }
            
            // Test report file creation
            $reportDir = '../reports/test/';
            if (!is_dir($reportDir)) {
                mkdir($reportDir, 0755, true);
            }
            
            $reportFile = $reportDir . 'integration_test_report.html';
            file_put_contents($reportFile, $htmlReport);
            
            if (!file_exists($reportFile) || filesize($reportFile) === 0) {
                throw new Exception('Failed to create report file');
            }
            
            // Cleanup
            unlink($reportFile);
            rmdir($reportDir);
            
            $this->addTest('Report Generation', 'HTML report generation successful with all data populated', true);
            
        } catch (Exception $e) {
            $this->addTest('Report Generation', 'Report generation test failed', false, $e->getMessage());
        }
    }
    
    private function testSystemIntegration() {
        try {
            // Test API endpoints
            $endpoints = [
                'system_status.php',
                'test_database.php',
                'test_file_processing.php',
                'test_data_extraction.php',
                'test_template.php'
            ];
            
            foreach ($endpoints as $endpoint) {
                $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $endpoint;
                
                // Use curl if available, otherwise skip
                if (function_exists('curl_init')) {
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($httpCode !== 200) {
                        throw new Exception("API endpoint $endpoint returned HTTP $httpCode");
                    }
                    
                    $data = json_decode($response, true);
                    if (!$data || !isset($data['success'])) {
                        throw new Exception("API endpoint $endpoint returned invalid response");
                    }
                }
            }
            
            $this->addTest('System Integration', 'All API endpoints accessible and returning valid responses', true);
            
        } catch (Exception $e) {
            $this->addTest('System Integration', 'System integration test failed', false, $e->getMessage());
        }
    }
    
    private function generateHTMLReport($data) {
        return '<!DOCTYPE html>
<html>
<head>
    <title>DICT After Training Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        h1, h2 { color: #0066cc; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f0f0f0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>DEPARTMENT OF INFORMATION AND COMMUNICATIONS TECHNOLOGY</h1>
        <h2>AFTER TRAINING REPORT</h2>
    </div>
    
    <h3>TRAINING DETAILS</h3>
    <table>
        <tr><th>Course Title</th><td>' . htmlspecialchars($data['course_title']) . '</td></tr>
        <tr><th>Training Date</th><td>' . htmlspecialchars($data['training_date']) . '</td></tr>
        <tr><th>Venue</th><td>' . htmlspecialchars($data['venue']) . '</td></tr>
        <tr><th>Total Participants</th><td>' . htmlspecialchars($data['total_participants']) . '</td></tr>
    </table>
    
    <h3>RATIONALE</h3>
    <p>' . htmlspecialchars($data['rationale'] ?? 'Not specified') . '</p>
    
    <h3>TRAINING OBJECTIVES</h3>
    <p>' . htmlspecialchars($data['objectives'] ?? 'Not specified') . '</p>
    
    <h3>TOPICS COVERED</h3>
    <p>' . htmlspecialchars($data['topics_covered'] ?? 'Not specified') . '</p>
    
    <h3>ISSUES/CONCERNS</h3>
    <p>' . htmlspecialchars($data['issues_concerns'] ?? 'None reported') . '</p>
    
    <h3>RECOMMENDATIONS</h3>
    <p>' . htmlspecialchars($data['recommendations'] ?? 'None provided') . '</p>
    
    <h3>ACTION PLANS</h3>
    <p>' . htmlspecialchars($data['action_plans'] ?? 'To be determined') . '</p>
</body>
</html>';
    }
    
    private function cleanup() {
        if ($this->testReportId && $this->db) {
            try {
                // Delete related records first (foreign key constraints)
                $this->db->prepare("DELETE FROM photo_documentation WHERE report_id = ?")->execute([$this->testReportId]);
                $this->db->prepare("DELETE FROM sector_categories WHERE report_id = ?")->execute([$this->testReportId]);
                $this->db->prepare("DELETE FROM uploaded_files WHERE report_id = ?")->execute([$this->testReportId]);
                $this->db->prepare("DELETE FROM activity_reports WHERE id = ?")->execute([$this->testReportId]);
            } catch (Exception $e) {
                // Cleanup errors are not critical for test results
            }
        }
    }
    
    private function addTest($name, $description, $passed, $error = null) {
        $this->tests[] = [
            'name' => $name,
            'description' => $description,
            'passed' => $passed,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $tester = new IntegrationTester();
    $result = $tester->runAllTests();
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
