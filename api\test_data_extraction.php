<?php
/**
 * Data Extraction Testing API - Tests data parsing and extraction functionality
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

class DataExtractionTester {
    private $tests = [];
    private $sampleTexts = [];
    
    public function __construct() {
        $this->prepareSampleTexts();
    }
    
    public function runAllTests() {
        $this->testCourseExtraction();
        $this->testDateExtraction();
        $this->testVenueExtraction();
        $this->testParticipantExtraction();
        $this->testObjectiveExtraction();
        $this->testPatternMatching();
        
        $passed = count(array_filter($this->tests, function($test) { return $test['passed']; }));
        $total = count($this->tests);
        
        return [
            'success' => true,
            'tests' => $this->tests,
            'summary' => [
                'total' => $total,
                'passed' => $passed,
                'failed' => $total - $passed,
                'success_rate' => $total > 0 ? round(($passed / $total) * 100, 1) : 0
            ]
        ];
    }
    
    private function prepareSampleTexts() {
        $this->sampleTexts = [
            'basic' => "Course Title: Advanced Web Development Training
Training Date: January 15, 2024
Venue: DICT Regional Office - Conference Room A
Total Participants: 25 (15 Male, 10 Female)
Objectives: To enhance web development skills and introduce modern frameworks",
            
            'detailed' => "DICT Post-Activity Report
Course: Digital Marketing Fundamentals
Date of Training: March 20-22, 2024
Location: Cebu City Convention Center
Participants: 
- Government Employees: 15
- Private Sector: 10
- Students: 5
Total: 30 participants
Male: 18, Female: 12
Training Objectives:
1. Understand digital marketing concepts
2. Learn social media marketing strategies
3. Implement SEO best practices",
            
            'alternative' => "Training Program: Cybersecurity Awareness
When: 2024-04-10 to 2024-04-12
Where: Manila Hotel, Rizal Ballroom
Attendees: 40 people (22 men, 18 women)
Goals: Improve cybersecurity knowledge and practices"
        ];
    }
    
    private function testCourseExtraction() {
        try {
            $patterns = [
                '/Course Title:\s*(.+?)(?:\n|$)/i',
                '/Course:\s*(.+?)(?:\n|$)/i',
                '/Training Program:\s*(.+?)(?:\n|$)/i'
            ];
            
            $expectedResults = [
                'Advanced Web Development Training',
                'Digital Marketing Fundamentals',
                'Cybersecurity Awareness'
            ];
            
            foreach ($this->sampleTexts as $key => $text) {
                $extracted = $this->extractWithPatterns($text, $patterns);
                $expected = $expectedResults[array_search($key, array_keys($this->sampleTexts))];
                
                if (trim($extracted) !== $expected) {
                    throw new Exception("Course extraction failed for '$key': expected '$expected', got '$extracted'");
                }
            }
            
            $this->addTest('Course Title Extraction', 'Successfully extracted course titles from all sample texts', true);
            
        } catch (Exception $e) {
            $this->addTest('Course Title Extraction', 'Course title extraction failed', false, $e->getMessage());
        }
    }
    
    private function testDateExtraction() {
        try {
            $patterns = [
                '/Training Date:\s*(.+?)(?:\n|$)/i',
                '/Date of Training:\s*(.+?)(?:\n|$)/i',
                '/When:\s*(.+?)(?:\n|$)/i'
            ];
            
            $expectedResults = [
                'January 15, 2024',
                'March 20-22, 2024',
                '2024-04-10 to 2024-04-12'
            ];
            
            foreach ($this->sampleTexts as $key => $text) {
                $extracted = $this->extractWithPatterns($text, $patterns);
                $expected = $expectedResults[array_search($key, array_keys($this->sampleTexts))];
                
                if (trim($extracted) !== $expected) {
                    throw new Exception("Date extraction failed for '$key': expected '$expected', got '$extracted'");
                }
            }
            
            $this->addTest('Training Date Extraction', 'Successfully extracted training dates from all sample texts', true);
            
        } catch (Exception $e) {
            $this->addTest('Training Date Extraction', 'Training date extraction failed', false, $e->getMessage());
        }
    }
    
    private function testVenueExtraction() {
        try {
            $patterns = [
                '/Venue:\s*(.+?)(?:\n|$)/i',
                '/Location:\s*(.+?)(?:\n|$)/i',
                '/Where:\s*(.+?)(?:\n|$)/i'
            ];
            
            $expectedResults = [
                'DICT Regional Office - Conference Room A',
                'Cebu City Convention Center',
                'Manila Hotel, Rizal Ballroom'
            ];
            
            foreach ($this->sampleTexts as $key => $text) {
                $extracted = $this->extractWithPatterns($text, $patterns);
                $expected = $expectedResults[array_search($key, array_keys($this->sampleTexts))];
                
                if (trim($extracted) !== $expected) {
                    throw new Exception("Venue extraction failed for '$key': expected '$expected', got '$extracted'");
                }
            }
            
            $this->addTest('Venue Extraction', 'Successfully extracted venues from all sample texts', true);
            
        } catch (Exception $e) {
            $this->addTest('Venue Extraction', 'Venue extraction failed', false, $e->getMessage());
        }
    }
    
    private function testParticipantExtraction() {
        try {
            // Test total participant extraction
            $totalPatterns = [
                '/Total Participants:\s*(\d+)/i',
                '/Total:\s*(\d+)/i',
                '/Attendees:\s*(\d+)/i'
            ];
            
            $expectedTotals = [25, 30, 40];
            
            foreach ($this->sampleTexts as $key => $text) {
                $extracted = $this->extractWithPatterns($text, $totalPatterns);
                $expected = $expectedTotals[array_search($key, array_keys($this->sampleTexts))];
                
                if ((int)$extracted !== $expected) {
                    throw new Exception("Total participant extraction failed for '$key': expected $expected, got $extracted");
                }
            }
            
            // Test gender breakdown extraction
            $malePatterns = ['/(\d+)\s*Male/i', '/Male:\s*(\d+)/i', '/(\d+)\s*men/i'];
            $femalePatterns = ['/(\d+)\s*Female/i', '/Female:\s*(\d+)/i', '/(\d+)\s*women/i'];
            
            $expectedMale = [15, 18, 22];
            $expectedFemale = [10, 12, 18];
            
            foreach ($this->sampleTexts as $key => $text) {
                $maleCount = $this->extractWithPatterns($text, $malePatterns);
                $femaleCount = $this->extractWithPatterns($text, $femalePatterns);
                
                $expectedM = $expectedMale[array_search($key, array_keys($this->sampleTexts))];
                $expectedF = $expectedFemale[array_search($key, array_keys($this->sampleTexts))];
                
                if ((int)$maleCount !== $expectedM || (int)$femaleCount !== $expectedF) {
                    throw new Exception("Gender breakdown extraction failed for '$key'");
                }
            }
            
            $this->addTest('Participant Count Extraction', 'Successfully extracted participant counts and gender breakdown', true);
            
        } catch (Exception $e) {
            $this->addTest('Participant Count Extraction', 'Participant count extraction failed', false, $e->getMessage());
        }
    }
    
    private function testObjectiveExtraction() {
        try {
            $patterns = [
                '/Objectives:\s*(.+?)(?:\n\n|\n[A-Z]|$)/is',
                '/Training Objectives:\s*(.+?)(?:\n\n|\n[A-Z]|$)/is',
                '/Goals:\s*(.+?)(?:\n\n|\n[A-Z]|$)/is'
            ];
            
            foreach ($this->sampleTexts as $key => $text) {
                $extracted = $this->extractWithPatterns($text, $patterns);
                
                if (empty(trim($extracted))) {
                    throw new Exception("Objective extraction failed for '$key': no objectives found");
                }
                
                // Check if extracted text contains relevant keywords
                $keywords = ['enhance', 'learn', 'understand', 'implement', 'improve'];
                $hasKeywords = false;
                foreach ($keywords as $keyword) {
                    if (stripos($extracted, $keyword) !== false) {
                        $hasKeywords = true;
                        break;
                    }
                }
                
                if (!$hasKeywords) {
                    throw new Exception("Objective extraction for '$key' doesn't contain expected keywords");
                }
            }
            
            $this->addTest('Objective Extraction', 'Successfully extracted training objectives from sample texts', true);
            
        } catch (Exception $e) {
            $this->addTest('Objective Extraction', 'Objective extraction failed', false, $e->getMessage());
        }
    }
    
    private function testPatternMatching() {
        try {
            // Test email pattern
            $emailText = "Contact: <EMAIL>.<NAME_EMAIL>";
            $emailPattern = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/';
            preg_match_all($emailPattern, $emailText, $emails);
            
            if (count($emails[0]) !== 2) {
                throw new Exception("Email pattern matching failed");
            }
            
            // Test phone pattern
            $phoneText = "Call us at +63-2-8888-4444 or 0917-123-4567";
            $phonePattern = '/(?:\+63-?|0)[\d-]{10,}/';
            preg_match_all($phonePattern, $phoneText, $phones);
            
            if (count($phones[0]) !== 2) {
                throw new Exception("Phone pattern matching failed");
            }
            
            // Test date pattern
            $dateText = "Dates: 2024-01-15, January 20, 2024, and 03/25/2024";
            $datePattern = '/\d{4}-\d{2}-\d{2}|\w+ \d{1,2}, \d{4}|\d{2}\/\d{2}\/\d{4}/';
            preg_match_all($datePattern, $dateText, $dates);
            
            if (count($dates[0]) !== 3) {
                throw new Exception("Date pattern matching failed");
            }
            
            $this->addTest('Pattern Matching', 'Regular expression patterns working correctly', true);
            
        } catch (Exception $e) {
            $this->addTest('Pattern Matching', 'Pattern matching test failed', false, $e->getMessage());
        }
    }
    
    private function extractWithPatterns($text, $patterns) {
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                return trim($matches[1]);
            }
        }
        return '';
    }
    
    private function addTest($name, $description, $passed, $error = null) {
        $this->tests[] = [
            'name' => $name,
            'description' => $description,
            'passed' => $passed,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $tester = new DataExtractionTester();
    $result = $tester->runAllTests();
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
