<?php
/**
 * Image Processing Utilities for Post-Activity Reports
 * Handles image resizing, organization, and metadata extraction
 */

class ImageProcessor {
    private $maxWidth = 800;
    private $maxHeight = 600;
    private $thumbnailWidth = 300;
    private $thumbnailHeight = 200;
    private $quality = 85;
    
    public function processImage($imagePath, $outputDir = null) {
        if (!file_exists($imagePath)) {
            throw new Exception('Image file not found: ' . $imagePath);
        }
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            throw new Exception('Invalid image file: ' . $imagePath);
        }
        
        $outputDir = $outputDir ?: dirname($imagePath) . '/processed/';
        if (!file_exists($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        $result = [
            'original_path' => $imagePath,
            'original_size' => filesize($imagePath),
            'original_dimensions' => ['width' => $imageInfo[0], 'height' => $imageInfo[1]],
            'mime_type' => $imageInfo['mime'],
            'processed_path' => null,
            'thumbnail_path' => null,
            'metadata' => $this->extractMetadata($imagePath)
        ];
        
        // Create processed version (resized if needed)
        $processedPath = $this->resizeImage($imagePath, $outputDir, $this->maxWidth, $this->maxHeight, 'processed_');
        if ($processedPath) {
            $result['processed_path'] = $processedPath;
        }
        
        // Create thumbnail
        $thumbnailPath = $this->resizeImage($imagePath, $outputDir, $this->thumbnailWidth, $this->thumbnailHeight, 'thumb_');
        if ($thumbnailPath) {
            $result['thumbnail_path'] = $thumbnailPath;
        }
        
        return $result;
    }
    
    private function resizeImage($sourcePath, $outputDir, $maxWidth, $maxHeight, $prefix = '') {
        $imageInfo = getimagesize($sourcePath);
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Calculate new dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        
        // If image is already smaller, don't resize
        if ($ratio >= 1 && $prefix !== 'thumb_') {
            return $sourcePath;
        }
        
        $newWidth = (int)($sourceWidth * $ratio);
        $newHeight = (int)($sourceHeight * $ratio);
        
        // Create source image resource
        $sourceImage = $this->createImageResource($sourcePath, $mimeType);
        if (!$sourceImage) {
            return false;
        }
        
        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }
        
        // Resize image
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
        
        // Generate output filename
        $pathInfo = pathinfo($sourcePath);
        $outputFilename = $prefix . $pathInfo['filename'] . '_' . $newWidth . 'x' . $newHeight . '.' . $pathInfo['extension'];
        $outputPath = $outputDir . $outputFilename;
        
        // Save image
        $saved = $this->saveImageResource($newImage, $outputPath, $mimeType);
        
        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        
        return $saved ? $outputPath : false;
    }
    
    private function createImageResource($imagePath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($imagePath);
            case 'image/png':
                return imagecreatefrompng($imagePath);
            case 'image/gif':
                return imagecreatefromgif($imagePath);
            case 'image/webp':
                if (function_exists('imagecreatefromwebp')) {
                    return imagecreatefromwebp($imagePath);
                }
                break;
        }
        return false;
    }
    
    private function saveImageResource($imageResource, $outputPath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagejpeg($imageResource, $outputPath, $this->quality);
            case 'image/png':
                return imagepng($imageResource, $outputPath);
            case 'image/gif':
                return imagegif($imageResource, $outputPath);
            case 'image/webp':
                if (function_exists('imagewebp')) {
                    return imagewebp($imageResource, $outputPath, $this->quality);
                }
                break;
        }
        return false;
    }
    
    public function extractMetadata($imagePath) {
        $metadata = [
            'file_size' => filesize($imagePath),
            'file_modified' => date('Y-m-d H:i:s', filemtime($imagePath)),
            'exif_data' => null,
            'date_taken' => null,
            'camera_info' => null,
            'gps_location' => null
        ];
        
        // Extract EXIF data for JPEG images
        if (function_exists('exif_read_data')) {
            $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
            if (in_array($extension, ['jpg', 'jpeg', 'tiff'])) {
                try {
                    $exif = exif_read_data($imagePath);
                    if ($exif) {
                        $metadata['exif_data'] = $exif;
                        
                        // Extract date taken
                        if (isset($exif['DateTime'])) {
                            $metadata['date_taken'] = $exif['DateTime'];
                        } elseif (isset($exif['DateTimeOriginal'])) {
                            $metadata['date_taken'] = $exif['DateTimeOriginal'];
                        }
                        
                        // Extract camera information
                        if (isset($exif['Make']) && isset($exif['Model'])) {
                            $metadata['camera_info'] = trim($exif['Make'] . ' ' . $exif['Model']);
                        }
                        
                        // Extract GPS location
                        if (isset($exif['GPSLatitude']) && isset($exif['GPSLongitude'])) {
                            $metadata['gps_location'] = $this->parseGPSCoordinates($exif);
                        }
                    }
                } catch (Exception $e) {
                    error_log("EXIF extraction error: " . $e->getMessage());
                }
            }
        }
        
        return $metadata;
    }
    
    private function parseGPSCoordinates($exif) {
        if (!isset($exif['GPSLatitude']) || !isset($exif['GPSLongitude']) ||
            !isset($exif['GPSLatitudeRef']) || !isset($exif['GPSLongitudeRef'])) {
            return null;
        }
        
        $latitude = $this->convertGPSCoordinate($exif['GPSLatitude'], $exif['GPSLatitudeRef']);
        $longitude = $this->convertGPSCoordinate($exif['GPSLongitude'], $exif['GPSLongitudeRef']);
        
        if ($latitude !== null && $longitude !== null) {
            return [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'formatted' => sprintf('%.6f, %.6f', $latitude, $longitude)
            ];
        }
        
        return null;
    }
    
    private function convertGPSCoordinate($coordinate, $hemisphere) {
        if (!is_array($coordinate) || count($coordinate) < 3) {
            return null;
        }
        
        $degrees = $this->evaluateFraction($coordinate[0]);
        $minutes = $this->evaluateFraction($coordinate[1]);
        $seconds = $this->evaluateFraction($coordinate[2]);
        
        $decimal = $degrees + ($minutes / 60) + ($seconds / 3600);
        
        if ($hemisphere === 'S' || $hemisphere === 'W') {
            $decimal *= -1;
        }
        
        return $decimal;
    }
    
    private function evaluateFraction($fraction) {
        if (strpos($fraction, '/') !== false) {
            $parts = explode('/', $fraction);
            if (count($parts) === 2 && $parts[1] != 0) {
                return $parts[0] / $parts[1];
            }
        }
        return (float)$fraction;
    }
    
    public function organizeImages($images, $reportId) {
        $organized = [
            'by_date' => [],
            'by_type' => [],
            'chronological' => []
        ];
        
        foreach ($images as $image) {
            $metadata = $image['metadata'] ?? [];
            $date = $this->extractImageDate($metadata, $image['original_path']);
            
            // Organize by date
            $dateKey = date('Y-m-d', strtotime($date));
            if (!isset($organized['by_date'][$dateKey])) {
                $organized['by_date'][$dateKey] = [];
            }
            $organized['by_date'][$dateKey][] = $image;
            
            // Organize by type (based on filename patterns)
            $type = $this->categorizeImage($image['original_path']);
            if (!isset($organized['by_type'][$type])) {
                $organized['by_type'][$type] = [];
            }
            $organized['by_type'][$type][] = $image;
            
            // Add to chronological list
            $image['sort_date'] = $date;
            $organized['chronological'][] = $image;
        }
        
        // Sort chronological list
        usort($organized['chronological'], function($a, $b) {
            return strtotime($a['sort_date']) - strtotime($b['sort_date']);
        });
        
        return $organized;
    }
    
    private function extractImageDate($metadata, $imagePath) {
        // Try EXIF date first
        if (isset($metadata['date_taken'])) {
            return $metadata['date_taken'];
        }
        
        // Try file modification date
        if (isset($metadata['file_modified'])) {
            return $metadata['file_modified'];
        }
        
        // Try to extract date from filename
        $filename = basename($imagePath);
        if (preg_match('/(\d{4}[-_]\d{2}[-_]\d{2})/', $filename, $matches)) {
            return str_replace('_', '-', $matches[1]);
        }
        
        // Default to file modification time
        return date('Y-m-d H:i:s', filemtime($imagePath));
    }
    
    private function categorizeImage($imagePath) {
        $filename = strtolower(basename($imagePath));
        
        if (preg_match('/(group|team|class|participants|attendees)/', $filename)) {
            return 'group_photos';
        } elseif (preg_match('/(presentation|screen|slide|demo)/', $filename)) {
            return 'presentations';
        } elseif (preg_match('/(activity|exercise|hands.?on|practical)/', $filename)) {
            return 'activities';
        } elseif (preg_match('/(venue|room|location|facility)/', $filename)) {
            return 'venue';
        } else {
            return 'general';
        }
    }
}
?>
