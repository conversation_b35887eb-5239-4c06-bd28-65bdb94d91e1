/**
 * System Testing JavaScript - Handles automated testing of the report generation system
 */

class SystemTester {
    constructor() {
        this.testResults = {
            database: [],
            fileProcessing: [],
            dataExtraction: [],
            template: [],
            integration: []
        };
        this.totalTests = 0;
        this.completedTests = 0;
        
        this.init();
    }
    
    init() {
        this.checkSystemStatus();
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Auto-refresh system status every 30 seconds
        setInterval(() => {
            this.checkSystemStatus();
        }, 30000);
    }
    
    async checkSystemStatus() {
        try {
            const response = await fetch('api/system_status.php');
            const result = await response.json();
            
            if (result.success) {
                this.updateSystemStatus(result.status);
            }
        } catch (error) {
            console.error('System status check failed:', error);
        }
    }
    
    updateSystemStatus(status) {
        document.getElementById('phpVersion').textContent = status.php_version || 'Unknown';
        document.getElementById('dbStatus').innerHTML = status.database ? 
            '<span class="text-success">Connected</span>' : 
            '<span class="text-danger">Disconnected</span>';
        document.getElementById('filePermissions').innerHTML = status.file_permissions ? 
            '<span class="text-success">OK</span>' : 
            '<span class="text-danger">Issues</span>';
    }
    
    updateProgress() {
        const percentage = this.totalTests > 0 ? (this.completedTests / this.totalTests) * 100 : 0;
        const progressBar = document.getElementById('testProgress');
        const progressText = document.getElementById('progressText');
        
        progressBar.style.width = percentage + '%';
        progressText.textContent = `${this.completedTests}/${this.totalTests} tests completed`;
        
        if (percentage === 100) {
            progressBar.classList.add('bg-success');
            progressText.textContent = 'All tests completed!';
        }
    }
    
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    async runDatabaseTests() {
        const resultsContainer = document.getElementById('dbTestResults');
        const statusBadge = document.getElementById('dbTestStatus');
        
        resultsContainer.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Running database tests...';
        
        try {
            const response = await fetch('api/test_database.php');
            const result = await response.json();
            
            this.testResults.database = result.tests || [];
            this.renderTestResults(resultsContainer, result.tests);
            this.updateTestStatus(statusBadge, result.tests);
            
        } catch (error) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Database tests failed: ${error.message}</div>`;
            statusBadge.className = 'badge bg-danger ms-auto';
            statusBadge.textContent = 'Failed';
        }
    }
    
    async runFileProcessingTests() {
        const resultsContainer = document.getElementById('fileTestResults');
        const statusBadge = document.getElementById('fileTestStatus');
        
        resultsContainer.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Running file processing tests...';
        
        try {
            const response = await fetch('api/test_file_processing.php');
            const result = await response.json();
            
            this.testResults.fileProcessing = result.tests || [];
            this.renderTestResults(resultsContainer, result.tests);
            this.updateTestStatus(statusBadge, result.tests);
            
        } catch (error) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">File processing tests failed: ${error.message}</div>`;
            statusBadge.className = 'badge bg-danger ms-auto';
            statusBadge.textContent = 'Failed';
        }
    }
    
    async runDataExtractionTests() {
        const resultsContainer = document.getElementById('extractionTestResults');
        const statusBadge = document.getElementById('extractionTestStatus');
        
        resultsContainer.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Running data extraction tests...';
        
        try {
            const response = await fetch('api/test_data_extraction.php');
            const result = await response.json();
            
            this.testResults.dataExtraction = result.tests || [];
            this.renderTestResults(resultsContainer, result.tests);
            this.updateTestStatus(statusBadge, result.tests);
            
        } catch (error) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Data extraction tests failed: ${error.message}</div>`;
            statusBadge.className = 'badge bg-danger ms-auto';
            statusBadge.textContent = 'Failed';
        }
    }
    
    async runTemplateTests() {
        const resultsContainer = document.getElementById('templateTestResults');
        const statusBadge = document.getElementById('templateTestStatus');
        
        resultsContainer.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Running template tests...';
        
        try {
            const response = await fetch('api/test_template.php');
            const result = await response.json();
            
            this.testResults.template = result.tests || [];
            this.renderTestResults(resultsContainer, result.tests);
            this.updateTestStatus(statusBadge, result.tests);
            
        } catch (error) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Template tests failed: ${error.message}</div>`;
            statusBadge.className = 'badge bg-danger ms-auto';
            statusBadge.textContent = 'Failed';
        }
    }
    
    async runIntegrationTests() {
        const resultsContainer = document.getElementById('integrationTestResults');
        const statusBadge = document.getElementById('integrationTestStatus');
        
        resultsContainer.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Running integration tests...';
        
        try {
            const response = await fetch('api/test_integration.php');
            const result = await response.json();
            
            this.testResults.integration = result.tests || [];
            this.renderTestResults(resultsContainer, result.tests);
            this.updateTestStatus(statusBadge, result.tests);
            
        } catch (error) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Integration tests failed: ${error.message}</div>`;
            statusBadge.className = 'badge bg-danger ms-auto';
            statusBadge.textContent = 'Failed';
        }
    }
    
    renderTestResults(container, tests) {
        if (!tests || tests.length === 0) {
            container.innerHTML = '<div class="alert alert-warning">No tests found</div>';
            return;
        }
        
        const html = tests.map(test => `
            <div class="test-result ${test.passed ? 'border-success' : 'border-danger'} border-start border-3 ps-3 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <strong>${test.name}</strong>
                    <span class="badge ${test.passed ? 'bg-success' : 'bg-danger'}">
                        ${test.passed ? 'PASS' : 'FAIL'}
                    </span>
                </div>
                <small class="text-muted">${test.description}</small>
                ${test.error ? `<div class="text-danger small mt-1">${test.error}</div>` : ''}
                ${test.details ? `<div class="text-info small mt-1">${test.details}</div>` : ''}
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    updateTestStatus(badge, tests) {
        if (!tests || tests.length === 0) {
            badge.className = 'badge bg-warning ms-auto';
            badge.textContent = 'No Tests';
            return;
        }
        
        const passed = tests.filter(t => t.passed).length;
        const total = tests.length;
        
        if (passed === total) {
            badge.className = 'badge bg-success ms-auto';
            badge.textContent = `${passed}/${total} Passed`;
        } else {
            badge.className = 'badge bg-danger ms-auto';
            badge.textContent = `${passed}/${total} Passed`;
        }
    }
    
    async runAllTests() {
        this.showAlert('Running all tests...', 'info');
        
        // Calculate total tests
        this.totalTests = 5; // Number of test categories
        this.completedTests = 0;
        
        try {
            await this.runDatabaseTests();
            this.completedTests++;
            this.updateProgress();
            
            await this.runFileProcessingTests();
            this.completedTests++;
            this.updateProgress();
            
            await this.runDataExtractionTests();
            this.completedTests++;
            this.updateProgress();
            
            await this.runTemplateTests();
            this.completedTests++;
            this.updateProgress();
            
            await this.runIntegrationTests();
            this.completedTests++;
            this.updateProgress();
            
            this.generateOverallResults();
            this.showAlert('All tests completed!', 'success');
            
        } catch (error) {
            this.showAlert('Test execution failed: ' + error.message, 'danger');
        }
    }
    
    generateOverallResults() {
        const container = document.getElementById('overallResults');
        
        let totalTests = 0;
        let passedTests = 0;
        
        Object.values(this.testResults).forEach(categoryTests => {
            categoryTests.forEach(test => {
                totalTests++;
                if (test.passed) passedTests++;
            });
        });
        
        const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
        
        const html = `
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="metric">
                        <h4 class="text-primary">${totalTests}</h4>
                        <small>Total Tests</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric">
                        <h4 class="text-success">${passedTests}</h4>
                        <small>Passed</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric">
                        <h4 class="text-danger">${totalTests - passedTests}</h4>
                        <small>Failed</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric">
                        <h4 class="${successRate >= 90 ? 'text-success' : successRate >= 70 ? 'text-warning' : 'text-danger'}">${successRate}%</h4>
                        <small>Success Rate</small>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    generateTestReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            results: this.testResults
        };
        
        // Create and download JSON report
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showAlert('Test report downloaded!', 'success');
    }
}

// Global functions for button clicks
let systemTester;

function runDatabaseTests() {
    systemTester.runDatabaseTests();
}

function runFileProcessingTests() {
    systemTester.runFileProcessingTests();
}

function runDataExtractionTests() {
    systemTester.runDataExtractionTests();
}

function runTemplateTests() {
    systemTester.runTemplateTests();
}

function runIntegrationTests() {
    systemTester.runIntegrationTests();
}

function runAllTests() {
    systemTester.runAllTests();
}

function generateTestReport() {
    systemTester.generateTestReport();
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    systemTester = new SystemTester();
});
