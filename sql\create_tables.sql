-- Post-Activity Report Generation System Database Schema
-- Based on DICT After Training Report Template

CREATE DATABASE IF NOT EXISTS post_activity_reports;
USE post_activity_reports;

-- Main activity reports table
CREATE TABLE activity_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_title VARCHAR(255),
    course_code VARCHAR(100),
    training_date DATE,
    training_time VARCHAR(50),
    duration VARCHAR(100),
    venue TEXT,
    resource_person VARCHAR(255),
    platform_used VARCHAR(100) DEFAULT 'Google Colab',
    mode VARCHAR(50) DEFAULT 'Face-to-Face',
    target_participants VARCHAR(255) DEFAULT 'DICT Personnel',
    total_attendees INT DEFAULT 0,
    male_attendees INT DEFAULT 0,
    female_attendees INT DEFAULT 0,
    beneficiaries_with_sex_disaggregation INT DEFAULT 0,
    rationale TEXT,
    objectives TEXT,
    topics_covered TEXT,
    issues_concerns TEXT,
    recommendations TEXT,
    action_plans TEXT,
    prepared_by <PERSON><PERSON><PERSON><PERSON>(255),
    prepared_by_designation VARCHAR(255),
    approved_by <PERSON><PERSON><PERSON><PERSON>(255),
    approved_by_designation VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sector categories breakdown
CREATE TABLE sector_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    sector_name VARCHAR(10), -- NGA, LGU, SUC, Others
    total_count INT DEFAULT 0,
    male_count INT DEFAULT 0,
    female_count INT DEFAULT 0,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);

-- Uploaded files tracking
CREATE TABLE uploaded_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    original_filename VARCHAR(255),
    stored_filename VARCHAR(255),
    file_path VARCHAR(500),
    file_type VARCHAR(100),
    file_size INT,
    upload_status ENUM('uploaded', 'processing', 'processed', 'failed') DEFAULT 'uploaded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);

-- Extracted data from files
CREATE TABLE extracted_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_id INT,
    data_type VARCHAR(100), -- course_title, objectives, rationale, etc.
    extracted_value TEXT,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES uploaded_files(id) ON DELETE CASCADE
);

-- Photo documentation
CREATE TABLE photo_documentation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    file_id INT,
    photo_date DATE,
    photo_caption VARCHAR(255),
    photo_path VARCHAR(500),
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES uploaded_files(id) ON DELETE CASCADE
);

-- Report generation log
CREATE TABLE report_generation_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    generation_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    generated_html_path VARCHAR(500),
    generated_pdf_path VARCHAR(500),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);

-- Insert default sector categories for new reports
INSERT INTO sector_categories (report_id, sector_name, total_count, male_count, female_count) VALUES
(0, 'NGA', 0, 0, 0),
(0, 'LGU', 0, 0, 0),
(0, 'SUC', 0, 0, 0),
(0, 'Others', 0, 0, 0);

-- Create indexes for better performance
CREATE INDEX idx_reports_created ON activity_reports(created_at);
CREATE INDEX idx_files_report ON uploaded_files(report_id);
CREATE INDEX idx_files_status ON uploaded_files(upload_status);
CREATE INDEX idx_extracted_file ON extracted_data(file_id);
CREATE INDEX idx_extracted_type ON extracted_data(data_type);
CREATE INDEX idx_photos_report ON photo_documentation(report_id);
CREATE INDEX idx_generation_report ON report_generation_log(report_id);
CREATE INDEX idx_generation_status ON report_generation_log(generation_status);
