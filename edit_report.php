<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Report Data - DICT Post-Activity Report System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <div class="header-section text-center py-4">
                    <img src="assets/images/dict-logo.png" alt="DICT Logo" class="logo mb-3" onerror="this.style.display='none'">
                    <h1 class="h3 mb-1">Department of Information and Communications Technology</h1>
                    <h2 class="h4 text-primary">Post-Activity Report System</h2>
                    <p class="text-muted">Edit Report Data</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit"></i> Edit Report Data
                            <span id="reportTitle" class="text-muted"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Alert Container -->
                        <div id="alertContainer"></div>

                        <!-- Edit Form -->
                        <form id="editReportForm">
                            <input type="hidden" id="reportId" name="reportId">
                            
                            <!-- Training Details Section -->
                            <div class="section-card mb-4">
                                <h6 class="section-title">
                                    <i class="fas fa-info-circle"></i> Training Details
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="courseTitle" class="form-label">Course Title *</label>
                                            <input type="text" class="form-control" id="courseTitle" name="course_title" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="courseCode" class="form-label">Course Code</label>
                                            <input type="text" class="form-control" id="courseCode" name="course_code">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="trainingDate" class="form-label">Training Date *</label>
                                            <input type="date" class="form-control" id="trainingDate" name="training_date" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="trainingTime" class="form-label">Training Time</label>
                                            <input type="text" class="form-control" id="trainingTime" name="training_time" placeholder="e.g., 9:00 AM - 5:00 PM">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="duration" class="form-label">Duration</label>
                                            <input type="text" class="form-control" id="duration" name="duration" placeholder="e.g., 8 hours">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="venue" class="form-label">Venue *</label>
                                            <input type="text" class="form-control" id="venue" name="venue" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="resourcePerson" class="form-label">Resource Person</label>
                                            <input type="text" class="form-control" id="resourcePerson" name="resource_person">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="platformUsed" class="form-label">Platform Used</label>
                                            <select class="form-select" id="platformUsed" name="platform_used">
                                                <option value="Google Colab">Google Colab</option>
                                                <option value="Zoom">Zoom</option>
                                                <option value="Microsoft Teams">Microsoft Teams</option>
                                                <option value="Face-to-Face">Face-to-Face</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="mode" class="form-label">Mode</label>
                                            <select class="form-select" id="mode" name="mode">
                                                <option value="Face-to-Face">Face-to-Face</option>
                                                <option value="Online">Online</option>
                                                <option value="Hybrid">Hybrid</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="targetParticipants" class="form-label">Target Participants</label>
                                            <input type="text" class="form-control" id="targetParticipants" name="target_participants" value="DICT Personnel">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Participants Section -->
                            <div class="section-card mb-4">
                                <h6 class="section-title">
                                    <i class="fas fa-users"></i> Participants Information
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="totalAttendees" class="form-label">Total Attendees</label>
                                            <input type="number" class="form-control" id="totalAttendees" name="total_attendees" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="maleAttendees" class="form-label">Male Attendees</label>
                                            <input type="number" class="form-control" id="maleAttendees" name="male_attendees" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="femaleAttendees" class="form-label">Female Attendees</label>
                                            <input type="number" class="form-control" id="femaleAttendees" name="female_attendees" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Content Sections -->
                            <div class="section-card mb-4">
                                <h6 class="section-title">
                                    <i class="fas fa-file-text"></i> Report Content
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="rationale" class="form-label">Rationale</label>
                                    <textarea class="form-control" id="rationale" name="rationale" rows="3" placeholder="Explain the purpose and importance of this training..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="objectives" class="form-label">Objectives</label>
                                    <textarea class="form-control" id="objectives" name="objectives" rows="4" placeholder="List the learning objectives..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="topicsCovered" class="form-label">Topics Covered</label>
                                    <textarea class="form-control" id="topicsCovered" name="topics_covered" rows="4" placeholder="Describe the topics and modules covered..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="issuesConcerns" class="form-label">Issues and Concerns</label>
                                    <textarea class="form-control" id="issuesConcerns" name="issues_concerns" rows="3" placeholder="Note any issues or concerns encountered..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="recommendations" class="form-label">Recommendations</label>
                                    <textarea class="form-control" id="recommendations" name="recommendations" rows="4" placeholder="Provide recommendations for improvement..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="actionPlans" class="form-label">Action Plans and Next Steps</label>
                                    <textarea class="form-control" id="actionPlans" name="action_plans" rows="4" placeholder="Outline the action plans and next steps..."></textarea>
                                </div>
                            </div>

                            <!-- Signatures Section -->
                            <div class="section-card mb-4">
                                <h6 class="section-title">
                                    <i class="fas fa-signature"></i> Signatures
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="preparedBy" class="form-label">Prepared By</label>
                                            <input type="text" class="form-control" id="preparedBy" name="prepared_by" value="COURSE OFFICER">
                                        </div>
                                        <div class="mb-3">
                                            <label for="preparedByDesignation" class="form-label">Designation</label>
                                            <input type="text" class="form-control" id="preparedByDesignation" name="prepared_by_designation" value="Training Management Division">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="approvedBy" class="form-label">Approved By</label>
                                            <input type="text" class="form-control" id="approvedBy" name="approved_by" value="OIC CHIEF">
                                        </div>
                                        <div class="mb-3">
                                            <label for="approvedByDesignation" class="form-label">Designation</label>
                                            <input type="text" class="form-control" id="approvedByDesignation" name="approved_by_designation" value="Training Management Division">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="button" class="btn btn-secondary" onclick="history.back()">
                                        <i class="fas fa-arrow-left"></i> Back
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-info me-2" id="previewBtn">
                                        <i class="fas fa-eye"></i> Preview Report
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Save Changes
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Help & Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="help-section">
                            <h6><i class="fas fa-lightbulb text-warning"></i> Tips for Better Reports</h6>
                            <ul class="small">
                                <li>Fill in all required fields marked with *</li>
                                <li>Use clear, professional language</li>
                                <li>Be specific in objectives and outcomes</li>
                                <li>Include actionable recommendations</li>
                                <li>Review all data before generating the final report</li>
                            </ul>
                        </div>
                        
                        <div class="help-section mt-4">
                            <h6><i class="fas fa-keyboard text-info"></i> Keyboard Shortcuts</h6>
                            <ul class="small">
                                <li><kbd>Ctrl + S</kbd> - Save changes</li>
                                <li><kbd>Ctrl + P</kbd> - Preview report</li>
                                <li><kbd>Esc</kbd> - Go back</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/edit_report.js"></script>
</body>
</html>
