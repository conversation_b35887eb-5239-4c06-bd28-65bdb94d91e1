<?php
/**
 * PDF Generation System with Multiple Fallback Methods
 * Supports wkhtmltopdf, DomPDF, mPDF, and browser-based generation
 */

class PDFGenerator {
    private $outputDir;
    private $tempDir;
    
    public function __construct($outputDir = '../generated_reports/', $tempDir = '../temp/') {
        $this->outputDir = rtrim($outputDir, '/') . '/';
        $this->tempDir = rtrim($tempDir, '/') . '/';
        
        // Create directories if they don't exist
        if (!file_exists($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
        if (!file_exists($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }
    
    public function generatePDF($htmlContent, $filename, $options = []) {
        $defaultOptions = [
            'format' => 'A4',
            'orientation' => 'portrait',
            'margin_top' => 20,
            'margin_right' => 20,
            'margin_bottom' => 20,
            'margin_left' => 20,
            'quality' => 'high'
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        // Ensure filename has .pdf extension
        if (!str_ends_with(strtolower($filename), '.pdf')) {
            $filename .= '.pdf';
        }
        
        $outputPath = $this->outputDir . $filename;
        
        // Try different PDF generation methods in order of preference
        $methods = [
            'wkhtmltopdf',
            'dompdf',
            'mpdf',
            'browser_print'
        ];
        
        foreach ($methods as $method) {
            try {
                $result = $this->{'generate' . ucfirst($method)}($htmlContent, $outputPath, $options);
                if ($result && file_exists($outputPath) && filesize($outputPath) > 0) {
                    return [
                        'success' => true,
                        'path' => $outputPath,
                        'method' => $method,
                        'size' => filesize($outputPath)
                    ];
                }
            } catch (Exception $e) {
                error_log("PDF generation failed with {$method}: " . $e->getMessage());
                continue;
            }
        }
        
        return [
            'success' => false,
            'message' => 'All PDF generation methods failed',
            'fallback_html' => $this->generatePrintableHTML($htmlContent, $filename)
        ];
    }
    
    private function generateWkhtmltopdf($htmlContent, $outputPath, $options) {
        if (!$this->commandExists('wkhtmltopdf')) {
            throw new Exception('wkhtmltopdf not available');
        }
        
        // Create temporary HTML file
        $tempHtml = $this->tempDir . 'temp_' . uniqid() . '.html';
        file_put_contents($tempHtml, $this->optimizeHTMLForPDF($htmlContent));
        
        // Build command
        $command = 'wkhtmltopdf';
        $command .= ' --page-size ' . escapeshellarg($options['format']);
        $command .= ' --orientation ' . escapeshellarg($options['orientation']);
        $command .= ' --margin-top ' . escapeshellarg($options['margin_top'] . 'mm');
        $command .= ' --margin-right ' . escapeshellarg($options['margin_right'] . 'mm');
        $command .= ' --margin-bottom ' . escapeshellarg($options['margin_bottom'] . 'mm');
        $command .= ' --margin-left ' . escapeshellarg($options['margin_left'] . 'mm');
        $command .= ' --disable-smart-shrinking';
        $command .= ' --print-media-type';
        $command .= ' --no-background';
        $command .= ' ' . escapeshellarg($tempHtml);
        $command .= ' ' . escapeshellarg($outputPath);
        
        exec($command, $output, $returnCode);
        
        // Clean up temp file
        if (file_exists($tempHtml)) {
            unlink($tempHtml);
        }
        
        return $returnCode === 0;
    }
    
    private function generateDompdf($htmlContent, $outputPath, $options) {
        if (!class_exists('Dompdf\Dompdf')) {
            throw new Exception('DomPDF not available');
        }
        
        $dompdf = new \Dompdf\Dompdf([
            'isRemoteEnabled' => true,
            'isHtml5ParserEnabled' => true,
            'isFontSubsettingEnabled' => true,
            'defaultFont' => 'Arial'
        ]);
        
        $dompdf->loadHtml($this->optimizeHTMLForPDF($htmlContent));
        $dompdf->setPaper($options['format'], $options['orientation']);
        $dompdf->render();
        
        $pdfContent = $dompdf->output();
        return file_put_contents($outputPath, $pdfContent) !== false;
    }
    
    private function generateMpdf($htmlContent, $outputPath, $options) {
        if (!class_exists('Mpdf\Mpdf')) {
            throw new Exception('mPDF not available');
        }
        
        $config = [
            'format' => $options['format'],
            'orientation' => $options['orientation'] === 'landscape' ? 'L' : 'P',
            'margin_left' => $options['margin_left'],
            'margin_right' => $options['margin_right'],
            'margin_top' => $options['margin_top'],
            'margin_bottom' => $options['margin_bottom'],
            'default_font' => 'Arial'
        ];
        
        $mpdf = new \Mpdf\Mpdf($config);
        $mpdf->WriteHTML($this->optimizeHTMLForPDF($htmlContent));
        $mpdf->Output($outputPath, 'F');
        
        return file_exists($outputPath);
    }
    
    private function generateBrowserPrint($htmlContent, $outputPath, $options) {
        // This method creates an HTML file optimized for browser printing
        $printHtml = $this->generatePrintableHTML($htmlContent, basename($outputPath, '.pdf'));
        $printPath = str_replace('.pdf', '_print.html', $outputPath);
        
        return file_put_contents($printPath, $printHtml) !== false;
    }
    
    private function optimizeHTMLForPDF($htmlContent) {
        // Add PDF-specific CSS
        $pdfCSS = '
        <style>
            @page {
                margin: 20mm;
                size: A4 portrait;
            }
            
            body {
                font-family: Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.3;
                color: #000;
                background: white;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .section {
                page-break-inside: avoid;
                margin-bottom: 15px;
            }
            
            .section-title {
                page-break-after: avoid;
            }
            
            .details-table,
            .participants-table {
                page-break-inside: avoid;
            }
            
            .photo-grid {
                page-break-inside: avoid;
            }
            
            .signatures {
                page-break-before: avoid;
                margin-top: 30px;
            }
            
            img {
                max-width: 100%;
                height: auto;
            }
            
            /* Ensure proper table borders for PDF */
            table {
                border-collapse: collapse;
                width: 100%;
            }
            
            td, th {
                border: 1px solid #000 !important;
                padding: 5px;
                vertical-align: top;
            }
            
            /* Hide elements that don\'t print well */
            .no-print {
                display: none !important;
            }
        </style>';
        
        // Insert CSS before closing head tag
        $htmlContent = str_replace('</head>', $pdfCSS . '</head>', $htmlContent);
        
        // Convert relative image paths to absolute paths
        $htmlContent = $this->convertImagePaths($htmlContent);
        
        return $htmlContent;
    }
    
    private function generatePrintableHTML($htmlContent, $filename) {
        $printCSS = '
        <style>
            @media print {
                body { margin: 0; }
                .report-container { max-width: none; }
                .section { page-break-inside: avoid; }
                .no-print { display: none !important; }
            }
            
            .print-instructions {
                background: #f0f8ff;
                border: 2px solid #4CAF50;
                padding: 15px;
                margin: 20px 0;
                border-radius: 5px;
                text-align: center;
            }
            
            .print-button {
                background: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin: 10px;
            }
            
            .print-button:hover {
                background: #45a049;
            }
        </style>
        
        <script>
            function printReport() {
                window.print();
            }
            
            function downloadPDF() {
                // This would trigger server-side PDF generation
                window.location.href = "api/generate.php?reportId=" + getReportId() + "&format=pdf";
            }
            
            function getReportId() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get("id") || "unknown";
            }
        </script>';
        
        $printInstructions = '
        <div class="print-instructions no-print">
            <h3>📄 Print Instructions</h3>
            <p>To save this report as PDF:</p>
            <ol style="text-align: left; display: inline-block;">
                <li>Click the "Print Report" button below</li>
                <li>In the print dialog, select "Save as PDF" or "Microsoft Print to PDF"</li>
                <li>Choose your desired location and click Save</li>
            </ol>
            <br>
            <button class="print-button" onclick="printReport()">🖨️ Print Report</button>
            <button class="print-button" onclick="downloadPDF()">📥 Try PDF Download</button>
        </div>';
        
        // Insert CSS and instructions
        $htmlContent = str_replace('</head>', $printCSS . '</head>', $htmlContent);
        $htmlContent = str_replace('<div class="report-container">', $printInstructions . '<div class="report-container">', $htmlContent);
        
        return $htmlContent;
    }
    
    private function convertImagePaths($htmlContent) {
        // Convert relative image paths to absolute paths
        $baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/';
        
        $htmlContent = preg_replace_callback(
            '/src=["\'](?!http|data:)([^"\']+)["\']/i',
            function($matches) use ($baseUrl) {
                return 'src="' . $baseUrl . ltrim($matches[1], '/') . '"';
            },
            $htmlContent
        );
        
        return $htmlContent;
    }
    
    private function commandExists($command) {
        $whereIsCommand = (PHP_OS_FAMILY === 'Windows') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ["pipe", "r"], // stdin
                1 => ["pipe", "w"], // stdout
                2 => ["pipe", "w"], // stderr
            ],
            $pipes
        );
        
        if ($process !== false) {
            $stdout = stream_get_contents($pipes[1]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            proc_close($process);
            
            return !empty(trim($stdout));
        }
        
        return false;
    }
    
    public function getAvailableMethods() {
        $methods = [];
        
        if ($this->commandExists('wkhtmltopdf')) {
            $methods[] = 'wkhtmltopdf';
        }
        
        if (class_exists('Dompdf\Dompdf')) {
            $methods[] = 'dompdf';
        }
        
        if (class_exists('Mpdf\Mpdf')) {
            $methods[] = 'mpdf';
        }
        
        $methods[] = 'browser_print'; // Always available
        
        return $methods;
    }
}
?>
