<?php
/**
 * Intelligent Data Parser for Post-Activity Reports
 * Extracts structured information from unstructured text
 */

class DataParser {
    private $patterns;
    
    public function __construct() {
        $this->initializePatterns();
    }
    
    private function initializePatterns() {
        $this->patterns = [
            'course_title' => [
                '/course\s*title[:\s]+([^\n\r]+)/i',
                '/training\s*title[:\s]+([^\n\r]+)/i',
                '/program\s*title[:\s]+([^\n\r]+)/i',
                '/title[:\s]+([^\n\r]+)/i',
                '/subject[:\s]+([^\n\r]+)/i'
            ],
            'course_code' => [
                '/course\s*code[:\s]+([A-Z0-9\-_]+)/i',
                '/program\s*code[:\s]+([A-Z0-9\-_]+)/i',
                '/code[:\s]+([A-Z0-9\-_]+)/i'
            ],
            'training_date' => [
                '/date[:\s]+([^\n\r]+)/i',
                '/training\s*date[:\s]+([^\n\r]+)/i',
                '/conducted\s*on[:\s]+([^\n\r]+)/i',
                '/held\s*on[:\s]+([^\n\r]+)/i',
                '/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i',
                '/(\w+\s+\d{1,2},?\s+\d{4})/i'
            ],
            'training_time' => [
                '/time[:\s]+([^\n\r]+)/i',
                '/schedule[:\s]+([^\n\r]+)/i',
                '/(\d{1,2}:\d{2}\s*(?:AM|PM))/i'
            ],
            'duration' => [
                '/duration[:\s]+([^\n\r]+)/i',
                '/(\d+)\s*days?/i',
                '/(\d+)\s*hours?/i',
                '/(\d+)\s*weeks?/i'
            ],
            'venue' => [
                '/venue[:\s]+([^\n\r]+)/i',
                '/location[:\s]+([^\n\r]+)/i',
                '/held\s*at[:\s]+([^\n\r]+)/i',
                '/conducted\s*at[:\s]+([^\n\r]+)/i',
                '/place[:\s]+([^\n\r]+)/i'
            ],
            'resource_person' => [
                '/resource\s*person[:\s]+([^\n\r]+)/i',
                '/facilitator[:\s]+([^\n\r]+)/i',
                '/instructor[:\s]+([^\n\r]+)/i',
                '/trainer[:\s]+([^\n\r]+)/i',
                '/speaker[:\s]+([^\n\r]+)/i',
                '/conducted\s*by[:\s]+([^\n\r]+)/i'
            ],
            'platform_used' => [
                '/platform[:\s]+([^\n\r]+)/i',
                '/using[:\s]+([^\n\r]+)/i',
                '/(zoom|google\s*meet|teams|webex|google\s*colab)/i'
            ],
            'mode' => [
                '/mode[:\s]+([^\n\r]+)/i',
                '/(face[\-\s]*to[\-\s]*face|online|virtual|hybrid|blended)/i'
            ],
            'target_participants' => [
                '/target\s*participants?[:\s]+([^\n\r]+)/i',
                '/participants?[:\s]+([^\n\r]+)/i',
                '/attendees?[:\s]+([^\n\r]+)/i',
                '/audience[:\s]+([^\n\r]+)/i'
            ],
            'total_attendees' => [
                '/total\s*(?:attendees?|participants?)[:\s]+(\d+)/i',
                '/(\d+)\s*(?:attendees?|participants?)/i',
                '/attendance[:\s]+(\d+)/i'
            ],
            'male_attendees' => [
                '/male[:\s]+(\d+)/i',
                '/(\d+)\s*male/i'
            ],
            'female_attendees' => [
                '/female[:\s]+(\d+)/i',
                '/(\d+)\s*female/i'
            ],
            'objectives' => [
                '/objectives?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/goals?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/aims?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/purpose[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
            ],
            'rationale' => [
                '/rationale[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/background[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/justification[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/reason[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
            ],
            'topics_covered' => [
                '/topics?\s*covered[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/curriculum[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/content[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/modules?[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
            ],
            'issues_concerns' => [
                '/issues?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/concerns?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/problems?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/challenges?[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
            ],
            'recommendations' => [
                '/recommendations?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/suggestions?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/proposed[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
            ],
            'action_plans' => [
                '/action\s*plans?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/next\s*steps?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
                '/follow[\-\s]*up[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
            ]
        ];
    }
    
    public function parseText($text) {
        $extractedData = [];
        
        foreach ($this->patterns as $field => $patternList) {
            $value = $this->extractField($text, $patternList);
            if ($value) {
                $extractedData[$field] = $this->postProcessValue($field, $value);
            }
        }
        
        // Post-processing for specific fields
        $extractedData = $this->postProcessData($extractedData);
        
        return $extractedData;
    }
    
    private function extractField($text, $patterns) {
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                return trim($matches[1]);
            }
        }
        return null;
    }
    
    private function postProcessValue($field, $value) {
        switch ($field) {
            case 'training_date':
                return $this->standardizeDate($value);
            
            case 'total_attendees':
            case 'male_attendees':
            case 'female_attendees':
                return (int) $value;
            
            case 'course_title':
            case 'venue':
            case 'resource_person':
                return $this->cleanTextValue($value);
            
            case 'objectives':
            case 'rationale':
            case 'topics_covered':
            case 'issues_concerns':
            case 'recommendations':
            case 'action_plans':
                return $this->cleanLongText($value);
            
            default:
                return trim($value);
        }
    }
    
    private function postProcessData($data) {
        // Calculate missing gender data if total is provided
        if (isset($data['total_attendees']) && 
            (!isset($data['male_attendees']) || !isset($data['female_attendees']))) {
            
            $total = $data['total_attendees'];
            $male = $data['male_attendees'] ?? 0;
            $female = $data['female_attendees'] ?? 0;
            
            if ($male + $female == 0) {
                // If no gender breakdown, assume roughly equal distribution
                $data['male_attendees'] = (int) ceil($total / 2);
                $data['female_attendees'] = $total - $data['male_attendees'];
            } elseif ($male == 0) {
                $data['male_attendees'] = $total - $female;
            } elseif ($female == 0) {
                $data['female_attendees'] = $total - $male;
            }
        }
        
        // Set default values for required fields
        $defaults = [
            'platform_used' => 'Google Colab',
            'mode' => 'Face-to-Face',
            'target_participants' => 'DICT Personnel'
        ];
        
        foreach ($defaults as $field => $defaultValue) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $data[$field] = $defaultValue;
            }
        }
        
        return $data;
    }
    
    private function standardizeDate($dateString) {
        // Try to parse various date formats
        $formats = [
            'Y-m-d',
            'm/d/Y',
            'd/m/Y',
            'M d, Y',
            'F d, Y',
            'd-m-Y',
            'Y/m/d'
        ];
        
        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $dateString);
            if ($date !== false) {
                return $date->format('Y-m-d');
            }
        }
        
        // If no format matches, try strtotime
        $timestamp = strtotime($dateString);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }
        
        return $dateString; // Return original if can't parse
    }
    
    private function cleanTextValue($text) {
        // Remove extra whitespace and clean up
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        // Remove common prefixes/suffixes
        $text = preg_replace('/^(title|name|by|at|in|on)[:\s]+/i', '', $text);
        
        return $text;
    }
    
    private function cleanLongText($text) {
        // Clean up long text fields
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        // Remove bullet points and numbering
        $text = preg_replace('/^[\d\.\-\*\•]+\s*/', '', $text);
        
        // Ensure proper sentence structure
        $text = preg_replace('/([.!?])\s*([a-z])/', '$1 ' . strtoupper('$2'), $text);
        
        return $text;
    }
    
    public function extractParticipantBreakdown($text) {
        $breakdown = [
            'NGA' => ['total' => 0, 'male' => 0, 'female' => 0],
            'LGU' => ['total' => 0, 'male' => 0, 'female' => 0],
            'SUC' => ['total' => 0, 'male' => 0, 'female' => 0],
            'Others' => ['total' => 0, 'male' => 0, 'female' => 0]
        ];
        
        // Look for sector-specific patterns
        $sectorPatterns = [
            'NGA' => '/NGA[:\s]+(\d+)(?:\s*\((\d+)\s*male?,?\s*(\d+)\s*female?\))?/i',
            'LGU' => '/LGU[:\s]+(\d+)(?:\s*\((\d+)\s*male?,?\s*(\d+)\s*female?\))?/i',
            'SUC' => '/SUC[:\s]+(\d+)(?:\s*\((\d+)\s*male?,?\s*(\d+)\s*female?\))?/i'
        ];
        
        foreach ($sectorPatterns as $sector => $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $breakdown[$sector]['total'] = (int) $matches[1];
                $breakdown[$sector]['male'] = isset($matches[2]) ? (int) $matches[2] : 0;
                $breakdown[$sector]['female'] = isset($matches[3]) ? (int) $matches[3] : 0;
            }
        }
        
        return $breakdown;
    }
}
?>
