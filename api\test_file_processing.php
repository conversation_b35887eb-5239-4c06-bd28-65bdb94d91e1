<?php
/**
 * File Processing Testing API - Tests file upload and processing functionality
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Mock FileProcessor class for testing
class FileProcessor {
    public function isValidFileType($mimeType) {
        $allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/csv',
            'image/jpeg',
            'image/png',
            'image/gif'
        ];

        return in_array($mimeType, $allowedTypes);
    }

    public function isValidFileSize($size) {
        $maxSize = 10 * 1024 * 1024; // 10MB
        return $size <= $maxSize;
    }

    public function extractTextFromFile($filePath, $mimeType) {
        if (!file_exists($filePath)) {
            throw new Exception("File not found: $filePath");
        }

        switch ($mimeType) {
            case 'text/plain':
            case 'text/csv':
                return file_get_contents($filePath);
            default:
                return "Mock extracted text from " . basename($filePath);
        }
    }
}

class FileProcessingTester {
    private $tests = [];
    private $testFilesDir = '../test_files/';
    
    public function __construct() {
        // Create test files directory if it doesn't exist
        if (!is_dir($this->testFilesDir)) {
            mkdir($this->testFilesDir, 0755, true);
        }
        
        $this->createTestFiles();
    }
    
    public function runAllTests() {
        $this->testFileValidation();
        $this->testTextExtraction();
        $this->testImageProcessing();
        $this->testFileUpload();
        $this->testErrorHandling();
        
        $passed = count(array_filter($this->tests, function($test) { return $test['passed']; }));
        $total = count($this->tests);
        
        return [
            'success' => true,
            'tests' => $this->tests,
            'summary' => [
                'total' => $total,
                'passed' => $passed,
                'failed' => $total - $passed,
                'success_rate' => $total > 0 ? round(($passed / $total) * 100, 1) : 0
            ]
        ];
    }
    
    private function createTestFiles() {
        // Create test text file
        $textContent = "Course Title: Advanced Web Development\nTraining Date: 2024-01-15\nVenue: DICT Training Center\nParticipants: 25";
        file_put_contents($this->testFilesDir . 'test.txt', $textContent);
        
        // Create test image (1x1 pixel PNG)
        $imageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
        file_put_contents($this->testFilesDir . 'test.png', $imageData);
        
        // Create test CSV file
        $csvContent = "Name,Position,Department\nJohn Doe,Developer,IT\nJane Smith,Analyst,IT";
        file_put_contents($this->testFilesDir . 'test.csv', $csvContent);
    }
    
    private function testFileValidation() {
        try {
            $processor = new FileProcessor();
            
            // Test valid file types
            $validTypes = ['application/pdf', 'application/msword', 'text/plain', 'image/jpeg', 'image/png'];
            foreach ($validTypes as $type) {
                if (!$processor->isValidFileType($type)) {
                    throw new Exception("Valid file type '$type' rejected");
                }
            }
            
            // Test invalid file types
            $invalidTypes = ['application/exe', 'text/html', 'video/mp4'];
            foreach ($invalidTypes as $type) {
                if ($processor->isValidFileType($type)) {
                    throw new Exception("Invalid file type '$type' accepted");
                }
            }
            
            // Test file size validation
            if (!$processor->isValidFileSize(1024 * 1024)) { // 1MB
                throw new Exception("Valid file size rejected");
            }
            
            if ($processor->isValidFileSize(50 * 1024 * 1024)) { // 50MB
                throw new Exception("Invalid file size accepted");
            }
            
            $this->addTest('File Validation', 'File type and size validation working correctly', true);
            
        } catch (Exception $e) {
            $this->addTest('File Validation', 'File validation test failed', false, $e->getMessage());
        }
    }
    
    private function testTextExtraction() {
        try {
            $processor = new FileProcessor();
            
            // Test text file extraction
            $textFile = $this->testFilesDir . 'test.txt';
            $extractedText = $processor->extractTextFromFile($textFile, 'text/plain');
            
            if (strpos($extractedText, 'Advanced Web Development') === false) {
                throw new Exception("Text extraction from TXT file failed");
            }
            
            // Test CSV file extraction
            $csvFile = $this->testFilesDir . 'test.csv';
            $extractedCsv = $processor->extractTextFromFile($csvFile, 'text/csv');
            
            if (strpos($extractedCsv, 'John Doe') === false) {
                throw new Exception("Text extraction from CSV file failed");
            }
            
            $this->addTest('Text Extraction', 'Text extraction from supported formats working', true);
            
        } catch (Exception $e) {
            $this->addTest('Text Extraction', 'Text extraction test failed', false, $e->getMessage());
        }
    }
    
    private function testImageProcessing() {
        try {
            if (!extension_loaded('gd')) {
                throw new Exception("GD extension not available");
            }
            
            $imageFile = $this->testFilesDir . 'test.png';
            
            // Test image info extraction
            $imageInfo = getimagesize($imageFile);
            if (!$imageInfo) {
                throw new Exception("Failed to get image information");
            }
            
            // Test image creation from file
            $image = imagecreatefrompng($imageFile);
            if (!$image) {
                throw new Exception("Failed to create image resource");
            }
            
            // Test image resizing
            $resized = imagescale($image, 50, 50);
            if (!$resized) {
                throw new Exception("Failed to resize image");
            }
            
            // Cleanup
            imagedestroy($image);
            imagedestroy($resized);
            
            $this->addTest('Image Processing', 'Image processing functions working correctly', true);
            
        } catch (Exception $e) {
            $this->addTest('Image Processing', 'Image processing test failed', false, $e->getMessage());
        }
    }
    
    private function testFileUpload() {
        try {
            // Test upload directory creation
            $uploadDir = '../uploads/test/';
            if (!is_dir($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    throw new Exception("Failed to create upload directory");
                }
            }
            
            // Test file copy operation
            $sourceFile = $this->testFilesDir . 'test.txt';
            $targetFile = $uploadDir . 'uploaded_test.txt';
            
            if (!copy($sourceFile, $targetFile)) {
                throw new Exception("Failed to copy file to upload directory");
            }
            
            // Test file exists and is readable
            if (!file_exists($targetFile) || !is_readable($targetFile)) {
                throw new Exception("Uploaded file not accessible");
            }
            
            // Cleanup
            unlink($targetFile);
            rmdir($uploadDir);
            
            $this->addTest('File Upload', 'File upload simulation successful', true);
            
        } catch (Exception $e) {
            $this->addTest('File Upload', 'File upload test failed', false, $e->getMessage());
        }
    }
    
    private function testErrorHandling() {
        try {
            $processor = new FileProcessor();
            
            // Test non-existent file
            try {
                $processor->extractTextFromFile('/non/existent/file.txt', 'text/plain');
                throw new Exception("Error handling failed - should have thrown exception for non-existent file");
            } catch (Exception $e) {
                // This is expected
                if (strpos($e->getMessage(), 'not found') === false && strpos($e->getMessage(), 'does not exist') === false) {
                    throw new Exception("Unexpected error message: " . $e->getMessage());
                }
            }
            
            // Test invalid file type
            if ($processor->isValidFileType('invalid/type')) {
                throw new Exception("Error handling failed - invalid file type accepted");
            }
            
            // Test oversized file
            if ($processor->isValidFileSize(100 * 1024 * 1024)) { // 100MB
                throw new Exception("Error handling failed - oversized file accepted");
            }
            
            $this->addTest('Error Handling', 'Error handling working correctly', true);
            
        } catch (Exception $e) {
            $this->addTest('Error Handling', 'Error handling test failed', false, $e->getMessage());
        }
    }
    
    private function addTest($name, $description, $passed, $error = null) {
        $this->tests[] = [
            'name' => $name,
            'description' => $description,
            'passed' => $passed,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    public function __destruct() {
        // Cleanup test files
        $files = glob($this->testFilesDir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        if (is_dir($this->testFilesDir)) {
            rmdir($this->testFilesDir);
        }
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $tester = new FileProcessingTester();
    $result = $tester->runAllTests();
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
