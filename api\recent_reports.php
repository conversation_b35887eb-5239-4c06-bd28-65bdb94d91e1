<?php
/**
 * Recent Reports API - Retrieves and manages recent reports
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class RecentReportsManager {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    public function getRecentReports($limit = 10) {
        try {
            $query = "SELECT 
                        ar.id,
                        ar.course_title,
                        ar.created_at,
                        ar.updated_at,
                        rgl.generation_status,
                        rgl.generated_html_path,
                        rgl.generated_pdf_path,
                        rgl.completed_at
                      FROM activity_reports ar
                      LEFT JOIN report_generation_log rgl ON ar.id = rgl.report_id
                      ORDER BY ar.updated_at DESC
                      LIMIT ?";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$limit]);
            $reports = $stmt->fetchAll();
            
            // Format the reports
            $formattedReports = array_map([$this, 'formatReport'], $reports);
            
            return [
                'success' => true,
                'reports' => $formattedReports
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function getReportDetails($reportId) {
        try {
            $query = "SELECT 
                        ar.*,
                        rgl.generation_status,
                        rgl.generated_html_path,
                        rgl.generated_pdf_path,
                        rgl.completed_at,
                        rgl.error_message
                      FROM activity_reports ar
                      LEFT JOIN report_generation_log rgl ON ar.id = rgl.report_id
                      WHERE ar.id = ?";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$reportId]);
            $report = $stmt->fetch();
            
            if (!$report) {
                throw new Exception('Report not found');
            }
            
            // Get sector data
            $sectorQuery = "SELECT * FROM sector_categories WHERE report_id = ? ORDER BY sector_name";
            $sectorStmt = $this->db->prepare($sectorQuery);
            $sectorStmt->execute([$reportId]);
            $sectors = $sectorStmt->fetchAll();
            
            // Get uploaded files
            $filesQuery = "SELECT * FROM uploaded_files WHERE report_id = ? ORDER BY created_at";
            $filesStmt = $this->db->prepare($filesQuery);
            $filesStmt->execute([$reportId]);
            $files = $filesStmt->fetchAll();
            
            // Get photos
            $photosQuery = "SELECT * FROM photo_documentation WHERE report_id = ? ORDER BY display_order, created_at";
            $photosStmt = $this->db->prepare($photosQuery);
            $photosStmt->execute([$reportId]);
            $photos = $photosStmt->fetchAll();
            
            return [
                'success' => true,
                'report' => $this->formatReport($report),
                'sectors' => $sectors,
                'files' => $files,
                'photos' => $photos
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function deleteReport($reportId) {
        try {
            $this->db->beginTransaction();
            
            // Get file paths before deletion
            $filesQuery = "SELECT file_path FROM uploaded_files WHERE report_id = ?";
            $filesStmt = $this->db->prepare($filesQuery);
            $filesStmt->execute([$reportId]);
            $files = $filesStmt->fetchAll();
            
            // Get generated report paths
            $reportQuery = "SELECT generated_html_path, generated_pdf_path FROM report_generation_log WHERE report_id = ?";
            $reportStmt = $this->db->prepare($reportQuery);
            $reportStmt->execute([$reportId]);
            $reportPaths = $reportStmt->fetch();
            
            // Delete database records (cascade will handle related tables)
            $deleteQuery = "DELETE FROM activity_reports WHERE id = ?";
            $deleteStmt = $this->db->prepare($deleteQuery);
            $deleteStmt->execute([$reportId]);
            
            $this->db->commit();
            
            // Delete physical files
            foreach ($files as $file) {
                if (file_exists($file['file_path'])) {
                    unlink($file['file_path']);
                }
            }
            
            // Delete generated reports
            if ($reportPaths) {
                if ($reportPaths['generated_html_path'] && file_exists($reportPaths['generated_html_path'])) {
                    unlink($reportPaths['generated_html_path']);
                }
                if ($reportPaths['generated_pdf_path'] && file_exists($reportPaths['generated_pdf_path'])) {
                    unlink($reportPaths['generated_pdf_path']);
                }
            }
            
            return [
                'success' => true,
                'message' => 'Report deleted successfully'
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function formatReport($report) {
        return [
            'id' => $report['id'],
            'course_title' => $report['course_title'] ?: 'Untitled Report',
            'created_at' => $report['created_at'],
            'updated_at' => $report['updated_at'] ?? $report['created_at'],
            'status' => $this->getReportStatus($report),
            'generation_status' => $report['generation_status'] ?? 'draft',
            'completed_at' => $report['completed_at'] ?? null,
            'has_pdf' => !empty($report['generated_pdf_path']) && file_exists($report['generated_pdf_path']),
            'has_html' => !empty($report['generated_html_path']) && file_exists($report['generated_html_path']),
            'pdf_path' => $report['generated_pdf_path'] ?? null,
            'html_path' => $report['generated_html_path'] ?? null
        ];
    }
    
    private function getReportStatus($report) {
        if (!empty($report['generation_status'])) {
            switch ($report['generation_status']) {
                case 'completed':
                    return 'completed';
                case 'processing':
                    return 'processing';
                case 'failed':
                    return 'failed';
                default:
                    return 'draft';
            }
        }
        
        // Determine status based on data completeness
        $requiredFields = ['course_title', 'training_date', 'venue'];
        $completedFields = 0;
        
        foreach ($requiredFields as $field) {
            if (!empty($report[$field])) {
                $completedFields++;
            }
        }
        
        if ($completedFields === count($requiredFields)) {
            return 'ready';
        } elseif ($completedFields > 0) {
            return 'partial';
        } else {
            return 'draft';
        }
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$manager = new RecentReportsManager();

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            $result = $manager->getReportDetails($_GET['id']);
        } else {
            $limit = $_GET['limit'] ?? 10;
            $result = $manager->getRecentReports($limit);
        }
        break;
        
    case 'DELETE':
        $input = json_decode(file_get_contents('php://input'), true);
        $reportId = $input['reportId'] ?? $_GET['id'] ?? null;
        
        if (!$reportId) {
            $result = ['success' => false, 'message' => 'Report ID is required'];
        } else {
            $result = $manager->deleteReport($reportId);
        }
        break;
        
    default:
        $result = ['success' => false, 'message' => 'Method not allowed'];
        break;
}

echo json_encode($result);
?>
