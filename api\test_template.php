<?php
/**
 * Template Testing API - Tests report template generation and PDF creation
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

class TemplateTester {
    private $tests = [];
    private $sampleData = [];
    
    public function __construct() {
        $this->prepareSampleData();
    }
    
    public function runAllTests() {
        $this->testTemplateStructure();
        $this->testDataPopulation();
        $this->testHTMLGeneration();
        $this->testPDFGeneration();
        $this->testImageIntegration();
        
        $passed = count(array_filter($this->tests, function($test) { return $test['passed']; }));
        $total = count($this->tests);
        
        return [
            'success' => true,
            'tests' => $this->tests,
            'summary' => [
                'total' => $total,
                'passed' => $passed,
                'failed' => $total - $passed,
                'success_rate' => $total > 0 ? round(($passed / $total) * 100, 1) : 0
            ]
        ];
    }
    
    private function prepareSampleData() {
        $this->sampleData = [
            'course_title' => 'Advanced Web Development Training',
            'training_date' => '2024-01-15',
            'venue' => 'DICT Regional Office',
            'total_participants' => 25,
            'male_participants' => 15,
            'female_participants' => 10,
            'rationale' => 'To enhance web development skills among government employees',
            'objectives' => 'Learn modern web technologies and frameworks',
            'topics_covered' => 'HTML5, CSS3, JavaScript, React, Node.js',
            'issues_concerns' => 'Limited internet connectivity during practical sessions',
            'recommendations' => 'Improve network infrastructure for future trainings',
            'action_plans' => 'Follow-up training sessions to be scheduled',
            'prepared_by' => 'John Doe',
            'position' => 'Training Coordinator',
            'date_prepared' => '2024-01-20'
        ];
    }
    
    private function testTemplateStructure() {
        try {
            $template = $this->generateTemplate($this->sampleData);
            
            // Check for required sections
            $requiredSections = [
                'DEPARTMENT OF INFORMATION AND COMMUNICATIONS TECHNOLOGY',
                'AFTER TRAINING REPORT',
                'TRAINING DETAILS',
                'RATIONALE',
                'TRAINING OBJECTIVES',
                'TOPICS COVERED',
                'ISSUES/CONCERNS',
                'RECOMMENDATIONS',
                'ACTION PLANS',
                'PHOTO DOCUMENTATION',
                'Prepared by:'
            ];
            
            foreach ($requiredSections as $section) {
                if (strpos($template, $section) === false) {
                    throw new Exception("Missing required section: $section");
                }
            }
            
            // Check for proper HTML structure
            if (!preg_match('/<html[^>]*>.*<\/html>/s', $template)) {
                throw new Exception("Invalid HTML structure");
            }
            
            // Check for CSS styling
            if (strpos($template, '<style>') === false && strpos($template, 'style=') === false) {
                throw new Exception("Missing CSS styling");
            }
            
            $this->addTest('Template Structure', 'Template contains all required sections and proper HTML structure', true);
            
        } catch (Exception $e) {
            $this->addTest('Template Structure', 'Template structure validation failed', false, $e->getMessage());
        }
    }
    
    private function testDataPopulation() {
        try {
            $template = $this->generateTemplate($this->sampleData);
            
            // Check if sample data is properly populated
            foreach ($this->sampleData as $key => $value) {
                if (is_string($value) && !empty($value)) {
                    if (strpos($template, $value) === false) {
                        throw new Exception("Data not populated: $key = $value");
                    }
                }
            }
            
            // Check for placeholder removal
            $placeholders = ['{{', '}}', '[PLACEHOLDER]', '[TO BE FILLED]'];
            foreach ($placeholders as $placeholder) {
                if (strpos($template, $placeholder) !== false) {
                    throw new Exception("Placeholder not replaced: $placeholder");
                }
            }
            
            $this->addTest('Data Population', 'Template data populated correctly without placeholders', true);
            
        } catch (Exception $e) {
            $this->addTest('Data Population', 'Data population test failed', false, $e->getMessage());
        }
    }
    
    private function testHTMLGeneration() {
        try {
            $template = $this->generateTemplate($this->sampleData);
            
            // Validate HTML
            $dom = new DOMDocument();
            libxml_use_internal_errors(true);
            $loaded = $dom->loadHTML($template);
            $errors = libxml_get_errors();
            libxml_clear_errors();
            
            if (!$loaded) {
                throw new Exception("Invalid HTML generated");
            }
            
            // Check for critical HTML elements
            $xpath = new DOMXPath($dom);
            
            // Check for tables
            $tables = $xpath->query('//table');
            if ($tables->length === 0) {
                throw new Exception("No tables found in template");
            }
            
            // Check for proper table structure
            $headerCells = $xpath->query('//th');
            if ($headerCells->length === 0) {
                throw new Exception("No table headers found");
            }
            
            // Check for styling
            $styledElements = $xpath->query('//*[@style]');
            if ($styledElements->length === 0) {
                $styleTag = $xpath->query('//style');
                if ($styleTag->length === 0) {
                    throw new Exception("No styling found in template");
                }
            }
            
            $this->addTest('HTML Generation', 'Valid HTML generated with proper structure and styling', true);
            
        } catch (Exception $e) {
            $this->addTest('HTML Generation', 'HTML generation test failed', false, $e->getMessage());
        }
    }
    
    private function testPDFGeneration() {
        try {
            // Test PDF generation methods availability
            $methods = [];
            
            // Check wkhtmltopdf
            $output = [];
            $return_var = 0;
            exec('wkhtmltopdf --version 2>&1', $output, $return_var);
            if ($return_var === 0) {
                $methods[] = 'wkhtmltopdf';
            }
            
            // Check DomPDF (composer package)
            if (class_exists('Dompdf\Dompdf')) {
                $methods[] = 'DomPDF';
            }
            
            // Check mPDF (composer package)
            if (class_exists('Mpdf\Mpdf')) {
                $methods[] = 'mPDF';
            }
            
            // Browser print method (always available)
            $methods[] = 'Browser Print';
            
            if (empty($methods)) {
                throw new Exception("No PDF generation methods available");
            }
            
            // Test HTML to PDF conversion simulation
            $template = $this->generateTemplate($this->sampleData);
            
            // Simulate PDF generation by checking HTML compatibility
            $pdfCompatible = true;
            
            // Check for PDF-incompatible elements
            if (strpos($template, '<script>') !== false) {
                $pdfCompatible = false;
            }
            
            // Check for proper page structure
            if (strpos($template, 'page-break') === false && strpos($template, '@page') === false) {
                // This is acceptable - not all templates need explicit page breaks
            }
            
            $this->addTest('PDF Generation', 'PDF generation methods available: ' . implode(', ', $methods), true, 
                          'Available methods: ' . implode(', ', $methods));
            
        } catch (Exception $e) {
            $this->addTest('PDF Generation', 'PDF generation test failed', false, $e->getMessage());
        }
    }
    
    private function testImageIntegration() {
        try {
            // Test image placeholder generation
            $imageData = [
                'images' => [
                    ['path' => 'test1.jpg', 'caption' => 'Training Session 1'],
                    ['path' => 'test2.jpg', 'caption' => 'Group Photo'],
                    ['path' => 'test3.jpg', 'caption' => 'Certificate Distribution']
                ]
            ];
            
            $template = $this->generateTemplate(array_merge($this->sampleData, $imageData));
            
            // Check for image elements
            if (strpos($template, '<img') === false) {
                throw new Exception("No image elements found in template");
            }
            
            // Check for image captions
            foreach ($imageData['images'] as $image) {
                if (strpos($template, $image['caption']) === false) {
                    throw new Exception("Image caption not found: " . $image['caption']);
                }
            }
            
            // Check for proper image grid structure
            if (strpos($template, 'photo-grid') === false && strpos($template, 'image-grid') === false) {
                // Check for alternative grid structures
                if (strpos($template, 'row') === false && strpos($template, 'col') === false) {
                    throw new Exception("No grid structure found for images");
                }
            }
            
            $this->addTest('Image Integration', 'Image integration working with proper grid layout and captions', true);
            
        } catch (Exception $e) {
            $this->addTest('Image Integration', 'Image integration test failed', false, $e->getMessage());
        }
    }
    
    private function generateTemplate($data) {
        // Simplified template generation for testing
        $template = '<!DOCTYPE html>
<html>
<head>
    <title>DICT After Training Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { width: 80px; height: 80px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .section { margin-bottom: 20px; }
        .photo-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }
        .photo-item img { width: 100%; height: auto; }
    </style>
</head>
<body>
    <div class="header">
        <img src="dict-logo.png" alt="DICT Logo" class="logo">
        <h1>DEPARTMENT OF INFORMATION AND COMMUNICATIONS TECHNOLOGY</h1>
        <h2>AFTER TRAINING REPORT</h2>
    </div>
    
    <div class="section">
        <h3>TRAINING DETAILS</h3>
        <table>
            <tr><th>Course Title</th><td>' . htmlspecialchars($data['course_title']) . '</td></tr>
            <tr><th>Training Date</th><td>' . htmlspecialchars($data['training_date']) . '</td></tr>
            <tr><th>Venue</th><td>' . htmlspecialchars($data['venue']) . '</td></tr>
            <tr><th>Total Participants</th><td>' . htmlspecialchars($data['total_participants']) . '</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h3>RATIONALE</h3>
        <p>' . htmlspecialchars($data['rationale']) . '</p>
    </div>
    
    <div class="section">
        <h3>TRAINING OBJECTIVES</h3>
        <p>' . htmlspecialchars($data['objectives']) . '</p>
    </div>
    
    <div class="section">
        <h3>TOPICS COVERED</h3>
        <p>' . htmlspecialchars($data['topics_covered']) . '</p>
    </div>
    
    <div class="section">
        <h3>ISSUES/CONCERNS</h3>
        <p>' . htmlspecialchars($data['issues_concerns']) . '</p>
    </div>
    
    <div class="section">
        <h3>RECOMMENDATIONS</h3>
        <p>' . htmlspecialchars($data['recommendations']) . '</p>
    </div>
    
    <div class="section">
        <h3>ACTION PLANS</h3>
        <p>' . htmlspecialchars($data['action_plans']) . '</p>
    </div>
    
    <div class="section">
        <h3>PHOTO DOCUMENTATION</h3>
        <div class="photo-grid">';
        
        if (isset($data['images'])) {
            foreach ($data['images'] as $image) {
                $template .= '<div class="photo-item">
                    <img src="' . htmlspecialchars($image['path']) . '" alt="' . htmlspecialchars($image['caption']) . '">
                    <p>' . htmlspecialchars($image['caption']) . '</p>
                </div>';
            }
        }
        
        $template .= '</div>
    </div>
    
    <div class="section">
        <p><strong>Prepared by:</strong> ' . htmlspecialchars($data['prepared_by']) . '</p>
        <p><strong>Position:</strong> ' . htmlspecialchars($data['position']) . '</p>
        <p><strong>Date:</strong> ' . htmlspecialchars($data['date_prepared']) . '</p>
    </div>
</body>
</html>';
        
        return $template;
    }
    
    private function addTest($name, $description, $passed, $error = null) {
        $this->tests[] = [
            'name' => $name,
            'description' => $description,
            'passed' => $passed,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $tester = new TemplateTester();
    $result = $tester->runAllTests();
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
