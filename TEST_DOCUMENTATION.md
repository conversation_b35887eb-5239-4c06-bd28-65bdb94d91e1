# DICT Post-Activity Report System - Test Documentation

## Overview
This document provides comprehensive testing information for the DICT Post-Activity Report Generation System. The system includes automated testing capabilities to validate all components and ensure reliable operation.

## Test Categories

### 1. Database Tests (`api/test_database.php`)
Tests database connectivity, schema validation, and CRUD operations.

**Test Cases:**
- **Database Connection**: Verifies connection to MySQL database
- **Table Structure**: Validates existence and structure of required tables
  - `activity_reports` - Main report data
  - `uploaded_files` - File upload records
  - `sector_categories` - Participant sector breakdown
  - `photo_documentation` - Image documentation
- **CRUD Operations**: Tests Create, Read, Update, Delete operations
- **Database Constraints**: Validates foreign key constraints and data integrity
- **Database Indexes**: Ensures proper indexing for performance

**Expected Results:**
- All tables exist with correct column structure
- Primary keys and foreign keys properly configured
- CRUD operations execute without errors
- Data integrity constraints enforced

### 2. File Processing Tests (`api/test_file_processing.php`)
Tests file upload handling, validation, and text extraction capabilities.

**Test Cases:**
- **File Validation**: Tests MIME type and file size validation
  - Supported: PDF, DOC, DOCX, TXT, CSV, JPG, PNG, GIF
  - Maximum size: 10MB
- **Text Extraction**: Tests extraction from various file formats
  - Plain text files
  - CSV files
  - PDF files (via pdftotext)
  - DOC files (via antiword)
  - DOCX files (via XML parsing)
- **Image Processing**: Tests image handling with GD library
  - Image information extraction
  - Image resizing and thumbnail generation
  - EXIF metadata extraction
- **File Upload Simulation**: Tests file copy and storage operations
- **Error Handling**: Tests proper error handling for invalid files

**Expected Results:**
- Valid file types accepted, invalid types rejected
- Text successfully extracted from supported formats
- Images processed correctly with proper resizing
- Appropriate error messages for invalid operations

### 3. Data Extraction Tests (`api/test_data_extraction.php`)
Tests intelligent parsing and data extraction from document content.

**Test Cases:**
- **Course Title Extraction**: Tests pattern matching for course names
  - Patterns: "Course Title:", "Course:", "Training Program:"
- **Date Extraction**: Tests various date format recognition
  - Formats: "January 15, 2024", "2024-01-15", "01/15/2024"
- **Venue Extraction**: Tests location/venue identification
  - Patterns: "Venue:", "Location:", "Where:"
- **Participant Count Extraction**: Tests numeric data extraction
  - Total participants, male/female breakdown
- **Objective Extraction**: Tests multi-line content extraction
- **Pattern Matching**: Tests regex patterns for emails, phones, dates

**Sample Test Data:**
```
Course Title: Advanced Web Development Training
Training Date: January 15, 2024
Venue: DICT Regional Office - Conference Room A
Total Participants: 25 (15 Male, 10 Female)
Objectives: To enhance web development skills and introduce modern frameworks
```

**Expected Results:**
- Accurate extraction of all data fields
- Proper handling of various text formats
- Robust pattern matching with high accuracy
- Graceful handling of missing or malformed data

### 4. Template Generation Tests (`api/test_template.php`)
Tests HTML template generation and PDF creation capabilities.

**Test Cases:**
- **Template Structure**: Validates HTML structure and required sections
  - DICT header and branding
  - Training details table
  - All required report sections
- **Data Population**: Tests proper data insertion into template
  - No remaining placeholders
  - Proper HTML escaping
- **HTML Generation**: Validates generated HTML
  - Valid HTML structure
  - Proper table formatting
  - CSS styling inclusion
- **PDF Generation**: Tests PDF creation methods
  - wkhtmltopdf (preferred)
  - DomPDF (fallback)
  - mPDF (fallback)
  - Browser print (final fallback)
- **Image Integration**: Tests photo documentation section
  - Image grid layout
  - Caption handling
  - Proper image sizing

**Expected Results:**
- Valid HTML with all required sections
- Proper data population without placeholders
- At least one PDF generation method available
- Images properly integrated with captions

### 5. Integration Tests (`api/test_integration.php`)
Tests end-to-end system functionality and component integration.

**Test Cases:**
- **End-to-End Workflow**: Complete process from data creation to report generation
  - Create test report in database
  - Add file records and extracted data
  - Generate complete report
  - Verify data integrity
- **File Upload to Report**: Integration between file processing and database
- **Data Extraction to Database**: Integration between parsing and storage
- **Report Generation**: Complete report creation with all components
- **System Integration**: API endpoint accessibility and response validation

**Expected Results:**
- Complete workflow executes without errors
- All components integrate properly
- Data flows correctly between system parts
- Generated reports contain all expected data

## Running Tests

### Automated Testing Interface
Access the testing interface at: `test_system.php`

**Features:**
- Interactive test execution
- Real-time progress tracking
- Detailed test results with pass/fail status
- System status monitoring
- Test report generation

### Manual Test Execution
Individual test APIs can be called directly:

```bash
# Database tests
curl http://localhost/post/api/test_database.php

# File processing tests
curl http://localhost/post/api/test_file_processing.php

# Data extraction tests
curl http://localhost/post/api/test_data_extraction.php

# Template tests
curl http://localhost/post/api/test_template.php

# Integration tests
curl http://localhost/post/api/test_integration.php
```

### System Status Check
Monitor system health:
```bash
curl http://localhost/post/api/system_status.php
```

## Test Requirements

### System Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Required PHP extensions:
  - PDO and PDO_MySQL
  - GD library
  - ZIP extension
  - JSON support
  - Mbstring
  - Fileinfo

### Directory Permissions
- `uploads/` - Read/write access for file uploads
- `reports/` - Read/write access for generated reports
- `temp/` - Read/write access for temporary files

### External Tools (Optional)
- `pdftotext` - For PDF text extraction
- `antiword` - For DOC file processing
- `wkhtmltopdf` - For high-quality PDF generation

## Test Data and Samples

### Sample Documents
The system includes test data generation for:
- Training activity documents
- Participant lists
- Course materials
- Evaluation forms

### Expected Extraction Results
Sample data patterns that should be successfully extracted:
- Course titles in various formats
- Date ranges and single dates
- Venue information with addresses
- Participant counts with demographics
- Training objectives and outcomes

## Troubleshooting

### Common Issues
1. **Database Connection Failures**
   - Check database credentials in `config/database.php`
   - Verify MySQL service is running
   - Confirm database exists and user has proper permissions

2. **File Processing Errors**
   - Verify file upload permissions
   - Check PHP upload limits (`upload_max_filesize`, `post_max_size`)
   - Ensure required PHP extensions are installed

3. **PDF Generation Issues**
   - Install wkhtmltopdf for best results
   - Check Composer dependencies for DomPDF/mPDF
   - Verify write permissions for report directory

4. **Text Extraction Problems**
   - Install pdftotext and antiword for better format support
   - Check file encoding (UTF-8 recommended)
   - Verify file integrity and format

### Performance Considerations
- Large file uploads may require increased PHP memory limits
- PDF generation can be resource-intensive
- Database queries are optimized with proper indexing
- Image processing may require additional memory for large images

## Validation Criteria

### Success Metrics
- **Database Tests**: 100% pass rate required
- **File Processing**: 95%+ pass rate acceptable
- **Data Extraction**: 90%+ accuracy for standard formats
- **Template Generation**: 100% pass rate required
- **Integration**: 95%+ pass rate acceptable

### Quality Assurance
- All critical paths must pass testing
- Error handling must be robust and informative
- Generated reports must match template specifications exactly
- System must handle edge cases gracefully

## Continuous Testing

### Automated Testing Schedule
- Run full test suite before any deployment
- Execute integration tests after system updates
- Perform regression testing when adding new features
- Monitor system status continuously in production

### Test Maintenance
- Update test cases when adding new features
- Maintain sample data relevance
- Review and update expected results
- Document any test modifications
