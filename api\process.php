<?php
/**
 * File Processing API - Processes uploaded files and extracts data
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../includes/text_extractor.php';
require_once '../includes/data_parser.php';

class FileProcessor {
    private $db;
    private $textExtractor;
    private $dataParser;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->textExtractor = new TextExtractor();
        $this->dataParser = new DataParser();
    }
    
    public function processFiles($reportId) {
        try {
            // Get all uploaded files for this report
            $files = $this->getUploadedFiles($reportId);
            
            if (empty($files)) {
                throw new Exception('No files found for processing');
            }
            
            $allExtractedData = [];
            
            // Process each file
            foreach ($files as $file) {
                $this->updateFileStatus($file['id'], 'processing');
                
                try {
                    // Extract text from file
                    $extractedText = $this->textExtractor->extractText($file['file_path'], $file['file_type']);
                    
                    if ($extractedText) {
                        // Parse extracted data
                        $parsedData = $this->dataParser->parseText($extractedText);
                        
                        // Store extracted data
                        $this->storeExtractedData($file['id'], $parsedData);
                        
                        // Merge with all extracted data
                        $allExtractedData = array_merge($allExtractedData, $parsedData);
                        
                        // Handle images separately
                        if (strpos($file['file_type'], 'image/') === 0) {
                            $this->processImage($file, $reportId);
                        }
                    }
                    
                    $this->updateFileStatus($file['id'], 'processed');
                    
                } catch (Exception $e) {
                    $this->updateFileStatus($file['id'], 'failed');
                    error_log("File processing failed for file {$file['id']}: " . $e->getMessage());
                }
            }
            
            // Update report with consolidated data
            $this->updateReportData($reportId, $allExtractedData);
            
            return [
                'success' => true,
                'message' => 'Files processed successfully',
                'extractedData' => $allExtractedData
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function getUploadedFiles($reportId) {
        $query = "SELECT * FROM uploaded_files WHERE report_id = ? AND upload_status IN ('uploaded', 'processing')";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
        return $stmt->fetchAll();
    }
    
    private function updateFileStatus($fileId, $status) {
        $query = "UPDATE uploaded_files SET upload_status = ? WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$status, $fileId]);
    }
    
    private function storeExtractedData($fileId, $data) {
        foreach ($data as $type => $value) {
            if (!empty($value)) {
                // Check if this data type already exists for this file
                $checkQuery = "SELECT id FROM extracted_data WHERE file_id = ? AND data_type = ?";
                $checkStmt = $this->db->prepare($checkQuery);
                $checkStmt->execute([$fileId, $type]);
                
                if ($checkStmt->fetch()) {
                    // Update existing record
                    $query = "UPDATE extracted_data SET extracted_value = ?, confidence_score = 0.8 WHERE file_id = ? AND data_type = ?";
                    $stmt = $this->db->prepare($query);
                    $stmt->execute([$value, $fileId, $type]);
                } else {
                    // Insert new record
                    $query = "INSERT INTO extracted_data (file_id, data_type, extracted_value, confidence_score) VALUES (?, ?, ?, 0.8)";
                    $stmt = $this->db->prepare($query);
                    $stmt->execute([$fileId, $type, $value]);
                }
            }
        }
    }
    
    private function processImage($file, $reportId) {
        // Extract date from filename or EXIF data
        $photoDate = $this->extractPhotoDate($file);
        
        // Generate caption
        $caption = $this->generatePhotoCaption($file);
        
        // Store in photo documentation
        $query = "INSERT INTO photo_documentation (report_id, file_id, photo_date, photo_caption, photo_path) VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId, $file['id'], $photoDate, $caption, $file['file_path']]);
    }
    
    private function extractPhotoDate($file) {
        // Try to extract date from EXIF data
        if (function_exists('exif_read_data') && in_array(strtolower(pathinfo($file['original_filename'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'tiff'])) {
            try {
                $exif = exif_read_data($file['file_path']);
                if ($exif && isset($exif['DateTime'])) {
                    $date = DateTime::createFromFormat('Y:m:d H:i:s', $exif['DateTime']);
                    if ($date) {
                        return $date->format('Y-m-d');
                    }
                }
            } catch (Exception $e) {
                // Ignore EXIF errors
            }
        }
        
        // Try to extract date from filename
        if (preg_match('/(\d{4}[-_]\d{2}[-_]\d{2})/', $file['original_filename'], $matches)) {
            return str_replace('_', '-', $matches[1]);
        }
        
        // Default to current date
        return date('Y-m-d');
    }
    
    private function generatePhotoCaption($file) {
        $filename = pathinfo($file['original_filename'], PATHINFO_FILENAME);
        
        // Clean up filename for caption
        $caption = preg_replace('/[_\-]/', ' ', $filename);
        $caption = preg_replace('/\d{4}[-_]\d{2}[-_]\d{2}/', '', $caption);
        $caption = trim($caption);
        
        if (empty($caption)) {
            $caption = 'Training Activity';
        }
        
        return ucwords($caption);
    }
    
    private function updateReportData($reportId, $extractedData) {
        // Prepare update fields
        $updateFields = [];
        $updateValues = [];
        
        $fieldMapping = [
            'course_title' => 'course_title',
            'course_code' => 'course_code',
            'training_date' => 'training_date',
            'training_time' => 'training_time',
            'duration' => 'duration',
            'venue' => 'venue',
            'resource_person' => 'resource_person',
            'platform_used' => 'platform_used',
            'mode' => 'mode',
            'target_participants' => 'target_participants',
            'total_attendees' => 'total_attendees',
            'male_attendees' => 'male_attendees',
            'female_attendees' => 'female_attendees',
            'objectives' => 'objectives',
            'rationale' => 'rationale',
            'topics_covered' => 'topics_covered',
            'issues_concerns' => 'issues_concerns',
            'recommendations' => 'recommendations',
            'action_plans' => 'action_plans'
        ];
        
        foreach ($fieldMapping as $extractedKey => $dbField) {
            if (isset($extractedData[$extractedKey]) && !empty($extractedData[$extractedKey])) {
                $updateFields[] = "$dbField = ?";
                $updateValues[] = $extractedData[$extractedKey];
            }
        }
        
        if (!empty($updateFields)) {
            $updateValues[] = $reportId;
            $query = "UPDATE activity_reports SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute($updateValues);
        }
        
        // Update sector categories if participant breakdown is available
        $this->updateSectorCategories($reportId, $extractedData);
    }
    
    private function updateSectorCategories($reportId, $extractedData) {
        // Extract participant breakdown
        $breakdown = $this->dataParser->extractParticipantBreakdown(implode(' ', $extractedData));
        
        foreach ($breakdown as $sector => $data) {
            if ($data['total'] > 0) {
                $query = "UPDATE sector_categories SET total_count = ?, male_count = ?, female_count = ? WHERE report_id = ? AND sector_name = ?";
                $stmt = $this->db->prepare($query);
                $stmt->execute([$data['total'], $data['male'], $data['female'], $reportId, $sector]);
            }
        }
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $reportId = $input['reportId'] ?? null;
    
    if (!$reportId) {
        echo json_encode(['success' => false, 'message' => 'Report ID is required']);
        exit;
    }
    
    $processor = new FileProcessor();
    $result = $processor->processFiles($reportId);
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
