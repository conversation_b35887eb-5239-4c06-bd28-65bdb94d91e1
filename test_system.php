<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Testing - DICT Post-Activity Report System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <div class="header-section text-center py-4">
                    <img src="assets/images/dict-logo.png" alt="DICT Logo" class="logo mb-3" onerror="this.style.display='none'">
                    <h1 class="h3 mb-1">Department of Information and Communications Technology</h1>
                    <h2 class="h4 text-primary">Post-Activity Report System</h2>
                    <p class="text-muted">System Testing & Validation</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-vial"></i> System Tests
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Alert Container -->
                        <div id="alertContainer"></div>

                        <!-- Test Categories -->
                        <div class="accordion" id="testAccordion">
                            <!-- Database Tests -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#databaseTests">
                                        <i class="fas fa-database me-2"></i> Database Connection & Schema Tests
                                        <span id="dbTestStatus" class="badge ms-auto"></span>
                                    </button>
                                </h2>
                                <div id="databaseTests" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <button class="btn btn-primary btn-sm" onclick="runDatabaseTests()">
                                            <i class="fas fa-play"></i> Run Database Tests
                                        </button>
                                        <div id="dbTestResults" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- File Processing Tests -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#fileTests">
                                        <i class="fas fa-file-alt me-2"></i> File Processing Tests
                                        <span id="fileTestStatus" class="badge ms-auto"></span>
                                    </button>
                                </h2>
                                <div id="fileTests" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <button class="btn btn-primary btn-sm" onclick="runFileProcessingTests()">
                                            <i class="fas fa-play"></i> Run File Processing Tests
                                        </button>
                                        <div id="fileTestResults" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Extraction Tests -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#extractionTests">
                                        <i class="fas fa-search me-2"></i> Data Extraction Tests
                                        <span id="extractionTestStatus" class="badge ms-auto"></span>
                                    </button>
                                </h2>
                                <div id="extractionTests" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <button class="btn btn-primary btn-sm" onclick="runDataExtractionTests()">
                                            <i class="fas fa-play"></i> Run Data Extraction Tests
                                        </button>
                                        <div id="extractionTestResults" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Template Generation Tests -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#templateTests">
                                        <i class="fas fa-file-pdf me-2"></i> Template & PDF Generation Tests
                                        <span id="templateTestStatus" class="badge ms-auto"></span>
                                    </button>
                                </h2>
                                <div id="templateTests" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <button class="btn btn-primary btn-sm" onclick="runTemplateTests()">
                                            <i class="fas fa-play"></i> Run Template Tests
                                        </button>
                                        <div id="templateTestResults" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Integration Tests -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#integrationTests">
                                        <i class="fas fa-cogs me-2"></i> Integration Tests
                                        <span id="integrationTestStatus" class="badge ms-auto"></span>
                                    </button>
                                </h2>
                                <div id="integrationTests" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <button class="btn btn-primary btn-sm" onclick="runIntegrationTests()">
                                            <i class="fas fa-play"></i> Run Integration Tests
                                        </button>
                                        <div id="integrationTestResults" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Overall Test Results -->
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-bar"></i> Overall Test Results
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="overallResults">
                                        <p class="text-muted">Run tests to see overall results</p>
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-success" onclick="runAllTests()">
                                            <i class="fas fa-play-circle"></i> Run All Tests
                                        </button>
                                        <button class="btn btn-info ms-2" onclick="generateTestReport()">
                                            <i class="fas fa-file-alt"></i> Generate Test Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Test Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="help-section">
                            <h6><i class="fas fa-list-check text-success"></i> Test Categories</h6>
                            <ul class="small">
                                <li><strong>Database Tests:</strong> Connection, schema validation, CRUD operations</li>
                                <li><strong>File Processing:</strong> Upload handling, format support, validation</li>
                                <li><strong>Data Extraction:</strong> Text parsing, pattern matching, accuracy</li>
                                <li><strong>Template Generation:</strong> HTML/PDF output, formatting</li>
                                <li><strong>Integration:</strong> End-to-end workflow testing</li>
                            </ul>
                        </div>
                        
                        <div class="help-section mt-4">
                            <h6><i class="fas fa-exclamation-triangle text-warning"></i> Test Requirements</h6>
                            <ul class="small">
                                <li>Database connection must be active</li>
                                <li>All required PHP extensions installed</li>
                                <li>Write permissions for uploads and reports</li>
                                <li>Sample test files available</li>
                            </ul>
                        </div>

                        <div class="help-section mt-4">
                            <h6><i class="fas fa-tools text-info"></i> System Status</h6>
                            <div id="systemStatus">
                                <div class="d-flex justify-content-between">
                                    <span>PHP Version:</span>
                                    <span id="phpVersion">Checking...</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Database:</span>
                                    <span id="dbStatus">Checking...</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>File Permissions:</span>
                                    <span id="filePermissions">Checking...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Progress -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-progress-bar"></i> Test Progress
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-2">
                            <div id="testProgress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-center small text-muted">Ready to start testing</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/test_system.js"></script>
</body>
</html>
