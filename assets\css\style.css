/* Post-Activity Report Generator Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container-fluid {
    padding: 2rem;
}

/* Header Styles */
.header-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.logo {
    width: 60px;
    height: 60px;
    object-fit: contain;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Drop Zone Styles */
.drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #fafbfc;
    cursor: pointer;
}

.drop-zone:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.drop-zone.dragover {
    border-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.1);
    transform: scale(1.02);
}

.drop-zone-content {
    pointer-events: none;
}

#fileInput {
    display: none;
}

/* File List Styles */
.file-list {
    max-height: 400px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    background-color: white;
    transition: all 0.2s ease;
}

.file-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.file-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.file-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.file-icon.pdf { background-color: rgba(220, 53, 69, 0.1); color: var(--danger-color); }
.file-icon.doc { background-color: rgba(13, 110, 253, 0.1); color: var(--primary-color); }
.file-icon.txt { background-color: rgba(25, 135, 84, 0.1); color: var(--success-color); }
.file-icon.img { background-color: rgba(255, 193, 7, 0.1); color: var(--warning-color); }

.file-details h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.file-details small {
    color: var(--secondary-color);
}

.file-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.uploaded { background-color: rgba(13, 202, 240, 0.1); color: var(--info-color); }
.status-badge.processing { background-color: rgba(255, 193, 7, 0.1); color: var(--warning-color); }
.status-badge.processed { background-color: rgba(25, 135, 84, 0.1); color: var(--success-color); }
.status-badge.failed { background-color: rgba(220, 53, 69, 0.1); color: var(--danger-color); }

/* Progress Styles */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* Action Buttons */
.action-buttons {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

/* Recent Reports */
.report-item {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.report-info {
    flex: 1;
}

.report-info h6 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.report-info small {
    color: var(--secondary-color);
}

.report-actions {
    margin-left: 1rem;
}

.report-actions .btn {
    margin-left: 0.125rem;
}

/* Help Section */
.help-content h6 {
    color: var(--dark-color);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.help-content ul {
    margin-bottom: 1rem;
}

.help-content li {
    margin-bottom: 0.25rem;
}

/* Status Messages */
.alert {
    border: none;
    border-radius: var(--border-radius);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .header-section {
        padding: 1.5rem;
        text-align: center;
    }
    
    .drop-zone {
        padding: 2rem 1rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .file-status {
        align-self: flex-end;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Edit Report Styles */
.section-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
}

.section-title {
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.help-section {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.help-section h6 {
    color: var(--dark-color);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.help-section ul {
    margin-bottom: 0;
    padding-left: 1.2rem;
}

.help-section li {
    margin-bottom: 0.25rem;
}

kbd {
    background-color: var(--dark-color);
    color: white;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

/* Form Validation */
.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .file-actions {
        margin-top: 0.5rem;
        margin-left: 0;
    }

    .report-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .report-actions {
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .section-card {
        padding: 0.75rem;
    }
}
