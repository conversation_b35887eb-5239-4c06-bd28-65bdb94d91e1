<?php
/**
 * Database Testing API - Tests database connectivity and operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../config/database.php';

class DatabaseTester {
    private $db;
    private $tests = [];
    
    public function __construct() {
        try {
            $database = new Database();
            $this->db = $database->getConnection();
        } catch (Exception $e) {
            $this->db = null;
        }
    }
    
    public function runAllTests() {
        $this->testConnection();
        $this->testTableStructure();
        $this->testCRUDOperations();
        $this->testConstraints();
        $this->testIndexes();
        
        $passed = count(array_filter($this->tests, function($test) { return $test['passed']; }));
        $total = count($this->tests);
        
        return [
            'success' => true,
            'tests' => $this->tests,
            'summary' => [
                'total' => $total,
                'passed' => $passed,
                'failed' => $total - $passed,
                'success_rate' => $total > 0 ? round(($passed / $total) * 100, 1) : 0
            ]
        ];
    }
    
    private function testConnection() {
        try {
            if (!$this->db) {
                throw new Exception('Database connection failed');
            }
            
            $stmt = $this->db->query("SELECT 1");
            $result = $stmt->fetch();
            
            if ($result) {
                $this->addTest('Database Connection', 'Successfully connected to database', true);
            } else {
                throw new Exception('Query execution failed');
            }
        } catch (Exception $e) {
            $this->addTest('Database Connection', 'Failed to connect to database', false, $e->getMessage());
        }
    }
    
    private function testTableStructure() {
        $requiredTables = [
            'activity_reports' => [
                'id', 'course_title', 'training_date', 'venue', 'created_at', 'updated_at'
            ],
            'uploaded_files' => [
                'id', 'report_id', 'filename', 'file_path', 'file_type', 'file_size'
            ],
            'sector_categories' => [
                'id', 'report_id', 'sector_name', 'total_count', 'male_count', 'female_count'
            ],
            'photo_documentation' => [
                'id', 'report_id', 'image_path', 'caption', 'date_taken'
            ]
        ];
        
        foreach ($requiredTables as $tableName => $requiredColumns) {
            try {
                // Check if table exists
                $stmt = $this->db->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$tableName]);
                
                if (!$stmt->fetch()) {
                    throw new Exception("Table '$tableName' does not exist");
                }
                
                // Check columns
                $stmt = $this->db->query("DESCRIBE $tableName");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $missingColumns = array_diff($requiredColumns, $columns);
                if (!empty($missingColumns)) {
                    throw new Exception("Missing columns in '$tableName': " . implode(', ', $missingColumns));
                }
                
                $this->addTest("Table Structure: $tableName", "Table exists with required columns", true);
                
            } catch (Exception $e) {
                $this->addTest("Table Structure: $tableName", "Table structure validation failed", false, $e->getMessage());
            }
        }
    }
    
    private function testCRUDOperations() {
        try {
            // Test INSERT
            $stmt = $this->db->prepare("
                INSERT INTO activity_reports (course_title, training_date, venue, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute(['Test Course', '2024-01-01', 'Test Venue']);
            $testId = $this->db->lastInsertId();
            
            if (!$testId) {
                throw new Exception('INSERT operation failed');
            }
            
            // Test SELECT
            $stmt = $this->db->prepare("SELECT * FROM activity_reports WHERE id = ?");
            $stmt->execute([$testId]);
            $record = $stmt->fetch();
            
            if (!$record || $record['course_title'] !== 'Test Course') {
                throw new Exception('SELECT operation failed');
            }
            
            // Test UPDATE
            $stmt = $this->db->prepare("UPDATE activity_reports SET course_title = ? WHERE id = ?");
            $stmt->execute(['Updated Test Course', $testId]);
            
            $stmt = $this->db->prepare("SELECT course_title FROM activity_reports WHERE id = ?");
            $stmt->execute([$testId]);
            $updated = $stmt->fetch();
            
            if (!$updated || $updated['course_title'] !== 'Updated Test Course') {
                throw new Exception('UPDATE operation failed');
            }
            
            // Test DELETE
            $stmt = $this->db->prepare("DELETE FROM activity_reports WHERE id = ?");
            $stmt->execute([$testId]);
            
            $stmt = $this->db->prepare("SELECT * FROM activity_reports WHERE id = ?");
            $stmt->execute([$testId]);
            $deleted = $stmt->fetch();
            
            if ($deleted) {
                throw new Exception('DELETE operation failed');
            }
            
            $this->addTest('CRUD Operations', 'All CRUD operations working correctly', true);
            
        } catch (Exception $e) {
            $this->addTest('CRUD Operations', 'CRUD operations test failed', false, $e->getMessage());
        }
    }
    
    private function testConstraints() {
        try {
            // Test foreign key constraints
            $stmt = $this->db->prepare("
                INSERT INTO activity_reports (course_title, training_date, venue, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute(['Constraint Test', '2024-01-01', 'Test Venue']);
            $reportId = $this->db->lastInsertId();
            
            // Test valid foreign key
            $stmt = $this->db->prepare("
                INSERT INTO uploaded_files (report_id, filename, file_path, file_type, file_size) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$reportId, 'test.pdf', '/test/path', 'application/pdf', 1024]);
            
            // Test invalid foreign key (should fail)
            try {
                $stmt->execute([99999, 'test2.pdf', '/test/path2', 'application/pdf', 1024]);
                throw new Exception('Foreign key constraint not enforced');
            } catch (PDOException $e) {
                // This is expected - foreign key constraint should prevent this
            }
            
            // Cleanup
            $this->db->prepare("DELETE FROM uploaded_files WHERE report_id = ?")->execute([$reportId]);
            $this->db->prepare("DELETE FROM activity_reports WHERE id = ?")->execute([$reportId]);
            
            $this->addTest('Database Constraints', 'Foreign key constraints working correctly', true);
            
        } catch (Exception $e) {
            $this->addTest('Database Constraints', 'Constraint test failed', false, $e->getMessage());
        }
    }
    
    private function testIndexes() {
        try {
            $tables = ['activity_reports', 'uploaded_files', 'sector_categories', 'photo_documentation'];
            
            foreach ($tables as $table) {
                $stmt = $this->db->query("SHOW INDEX FROM $table");
                $indexes = $stmt->fetchAll();
                
                // Check for primary key
                $hasPrimaryKey = false;
                foreach ($indexes as $index) {
                    if ($index['Key_name'] === 'PRIMARY') {
                        $hasPrimaryKey = true;
                        break;
                    }
                }
                
                if (!$hasPrimaryKey) {
                    throw new Exception("Table '$table' missing primary key");
                }
            }
            
            $this->addTest('Database Indexes', 'Primary keys exist on all tables', true);
            
        } catch (Exception $e) {
            $this->addTest('Database Indexes', 'Index validation failed', false, $e->getMessage());
        }
    }
    
    private function addTest($name, $description, $passed, $error = null) {
        $this->tests[] = [
            'name' => $name,
            'description' => $description,
            'passed' => $passed,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $tester = new DatabaseTester();
    $result = $tester->runAllTests();
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
