<?php
/**
 * Report Generation API - Generates final HTML and PDF reports
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../includes/report_template.php';

class ReportGenerator {
    private $db;
    private $template;
    private $outputDir = '../generated_reports/';
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->template = new ReportTemplate();
        
        // Create output directory if it doesn't exist
        if (!file_exists($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
    }
    
    public function generateReport($reportId) {
        try {
            // Check if report exists
            if (!$this->reportExists($reportId)) {
                throw new Exception('Report not found');
            }
            
            // Update generation status
            $this->updateGenerationStatus($reportId, 'processing');
            
            // Generate HTML report
            $htmlContent = $this->template->generateReport($reportId);
            
            // Save HTML file
            $htmlFilename = "report_{$reportId}_" . date('Y-m-d_H-i-s') . '.html';
            $htmlPath = $this->outputDir . $htmlFilename;
            file_put_contents($htmlPath, $htmlContent);
            
            // Generate PDF
            $pdfPath = $this->generatePDF($htmlPath, $reportId);
            
            // Update generation log
            $this->updateGenerationLog($reportId, 'completed', $htmlPath, $pdfPath);
            
            return [
                'success' => true,
                'message' => 'Report generated successfully',
                'reportUrl' => 'view_report.php?id=' . $reportId,
                'htmlPath' => $htmlPath,
                'pdfPath' => $pdfPath
            ];
            
        } catch (Exception $e) {
            $this->updateGenerationStatus($reportId, 'failed', $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function reportExists($reportId) {
        $query = "SELECT id FROM activity_reports WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
        return $stmt->fetch() !== false;
    }
    
    private function updateGenerationStatus($reportId, $status, $errorMessage = null) {
        // Check if log entry exists
        $checkQuery = "SELECT id FROM report_generation_log WHERE report_id = ?";
        $checkStmt = $this->db->prepare($checkQuery);
        $checkStmt->execute([$reportId]);
        
        if ($checkStmt->fetch()) {
            // Update existing entry
            $query = "UPDATE report_generation_log SET generation_status = ?, error_message = ? WHERE report_id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$status, $errorMessage, $reportId]);
        } else {
            // Create new entry
            $query = "INSERT INTO report_generation_log (report_id, generation_status, error_message) VALUES (?, ?, ?)";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$reportId, $status, $errorMessage]);
        }
    }
    
    private function updateGenerationLog($reportId, $status, $htmlPath, $pdfPath) {
        $query = "UPDATE report_generation_log SET 
                  generation_status = ?, 
                  generated_html_path = ?, 
                  generated_pdf_path = ?, 
                  completed_at = NOW() 
                  WHERE report_id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$status, $htmlPath, $pdfPath, $reportId]);
    }
    
    private function generatePDF($htmlPath, $reportId) {
        $pdfFilename = "report_{$reportId}_" . date('Y-m-d_H-i-s') . '.pdf';
        $pdfPath = $this->outputDir . $pdfFilename;
        
        // Method 1: Try wkhtmltopdf (best quality)
        if ($this->commandExists('wkhtmltopdf')) {
            $command = "wkhtmltopdf --page-size A4 --margin-top 0.75in --margin-right 0.75in --margin-bottom 0.75in --margin-left 0.75in " . 
                      escapeshellarg($htmlPath) . " " . escapeshellarg($pdfPath);
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($pdfPath)) {
                return $pdfPath;
            }
        }
        
        // Method 2: Try using DomPDF library if available
        if (class_exists('Dompdf\Dompdf')) {
            try {
                $dompdf = new \Dompdf\Dompdf();
                $dompdf->loadHtml(file_get_contents($htmlPath));
                $dompdf->setPaper('A4', 'portrait');
                $dompdf->render();
                
                file_put_contents($pdfPath, $dompdf->output());
                
                if (file_exists($pdfPath)) {
                    return $pdfPath;
                }
            } catch (Exception $e) {
                error_log("DomPDF error: " . $e->getMessage());
            }
        }
        
        // Method 3: Try using mPDF library if available
        if (class_exists('Mpdf\Mpdf')) {
            try {
                $mpdf = new \Mpdf\Mpdf([
                    'format' => 'A4',
                    'margin_left' => 20,
                    'margin_right' => 20,
                    'margin_top' => 20,
                    'margin_bottom' => 20
                ]);
                
                $mpdf->WriteHTML(file_get_contents($htmlPath));
                $mpdf->Output($pdfPath, 'F');
                
                if (file_exists($pdfPath)) {
                    return $pdfPath;
                }
            } catch (Exception $e) {
                error_log("mPDF error: " . $e->getMessage());
            }
        }
        
        // Method 4: Fallback - create a simple HTML file for printing
        $printHtmlPath = str_replace('.pdf', '_print.html', $pdfPath);
        $htmlContent = file_get_contents($htmlPath);
        
        // Add print-specific CSS
        $printCSS = '
        <style>
            @media print {
                body { margin: 0; }
                .report-container { max-width: none; }
                .section { page-break-inside: avoid; }
            }
        </style>
        <script>
            window.onload = function() {
                window.print();
            }
        </script>';
        
        $htmlContent = str_replace('</head>', $printCSS . '</head>', $htmlContent);
        file_put_contents($printHtmlPath, $htmlContent);
        
        return $printHtmlPath;
    }
    
    private function commandExists($command) {
        $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ["pipe", "r"], // stdin
                1 => ["pipe", "w"], // stdout
                2 => ["pipe", "w"], // stderr
            ],
            $pipes
        );
        
        if ($process !== false) {
            $stdout = stream_get_contents($pipes[1]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            proc_close($process);
            
            return !empty(trim($stdout));
        }
        
        return false;
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $reportId = $input['reportId'] ?? null;
    
    if (!$reportId) {
        echo json_encode(['success' => false, 'message' => 'Report ID is required']);
        exit;
    }
    
    $generator = new ReportGenerator();
    $result = $generator->generateReport($reportId);
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
