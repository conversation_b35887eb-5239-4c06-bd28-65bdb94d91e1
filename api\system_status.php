<?php
/**
 * System Status API - Checks system health and requirements
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../config/database.php';

class SystemStatus {
    private $db;
    
    public function __construct() {
        try {
            $database = new Database();
            $this->db = $database->getConnection();
        } catch (Exception $e) {
            $this->db = null;
        }
    }
    
    public function getStatus() {
        $status = [
            'php_version' => PHP_VERSION,
            'database' => $this->checkDatabase(),
            'file_permissions' => $this->checkFilePermissions(),
            'required_extensions' => $this->checkRequiredExtensions(),
            'memory_limit' => ini_get('memory_limit'),
            'max_file_size' => ini_get('upload_max_filesize'),
            'max_post_size' => ini_get('post_max_size')
        ];
        
        return [
            'success' => true,
            'status' => $status,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    private function checkDatabase() {
        if (!$this->db) {
            return false;
        }
        
        try {
            // Test connection
            $stmt = $this->db->query("SELECT 1");
            
            // Check if tables exist
            $tables = ['activity_reports', 'uploaded_files', 'sector_categories', 'photo_documentation'];
            foreach ($tables as $table) {
                $stmt = $this->db->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if (!$stmt->fetch()) {
                    return false;
                }
            }
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function checkFilePermissions() {
        $directories = ['uploads/', 'reports/', 'temp/'];
        
        foreach ($directories as $dir) {
            $fullPath = '../' . $dir;
            
            // Check if directory exists
            if (!is_dir($fullPath)) {
                if (!mkdir($fullPath, 0755, true)) {
                    return false;
                }
            }
            
            // Check if writable
            if (!is_writable($fullPath)) {
                return false;
            }
        }
        
        return true;
    }
    
    private function checkRequiredExtensions() {
        $required = [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'gd' => extension_loaded('gd'),
            'zip' => extension_loaded('zip'),
            'json' => extension_loaded('json'),
            'mbstring' => extension_loaded('mbstring'),
            'fileinfo' => extension_loaded('fileinfo')
        ];
        
        return $required;
    }
    
    public function getDetailedInfo() {
        $info = [
            'php_info' => [
                'version' => PHP_VERSION,
                'sapi' => php_sapi_name(),
                'os' => PHP_OS,
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'max_file_uploads' => ini_get('max_file_uploads')
            ],
            'database_info' => $this->getDatabaseInfo(),
            'file_system' => $this->getFileSystemInfo(),
            'extensions' => $this->checkRequiredExtensions(),
            'external_tools' => $this->checkExternalTools()
        ];
        
        return [
            'success' => true,
            'info' => $info
        ];
    }
    
    private function getDatabaseInfo() {
        if (!$this->db) {
            return ['status' => 'disconnected'];
        }
        
        try {
            $stmt = $this->db->query("SELECT VERSION() as version");
            $version = $stmt->fetch()['version'];
            
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM activity_reports");
            $reportCount = $stmt->fetch()['count'];
            
            return [
                'status' => 'connected',
                'version' => $version,
                'report_count' => $reportCount
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function getFileSystemInfo() {
        $info = [];
        $directories = ['uploads/', 'reports/', 'temp/'];
        
        foreach ($directories as $dir) {
            $fullPath = '../' . $dir;
            $info[$dir] = [
                'exists' => is_dir($fullPath),
                'writable' => is_writable($fullPath),
                'size' => is_dir($fullPath) ? $this->getDirSize($fullPath) : 0
            ];
        }
        
        return $info;
    }
    
    private function getDirSize($dir) {
        $size = 0;
        if (is_dir($dir)) {
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($files as $file) {
                $size += $file->getSize();
            }
        }
        return $size;
    }
    
    private function checkExternalTools() {
        $tools = [];
        
        // Check for pdftotext
        $output = [];
        $return_var = 0;
        exec('pdftotext -v 2>&1', $output, $return_var);
        $tools['pdftotext'] = $return_var === 0 || $return_var === 99; // 99 is normal for version check
        
        // Check for antiword
        exec('antiword -h 2>&1', $output, $return_var);
        $tools['antiword'] = $return_var === 0 || strpos(implode(' ', $output), 'antiword') !== false;
        
        // Check for wkhtmltopdf
        exec('wkhtmltopdf --version 2>&1', $output, $return_var);
        $tools['wkhtmltopdf'] = $return_var === 0;
        
        return $tools;
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $statusChecker = new SystemStatus();
    
    if (isset($_GET['detailed'])) {
        $result = $statusChecker->getDetailedInfo();
    } else {
        $result = $statusChecker->getStatus();
    }
    
    echo json_encode($result);
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
