<?php
/**
 * Update Report API - Updates report data from the edit interface
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class ReportUpdater {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    public function updateReport($data) {
        try {
            if (!isset($data['reportId']) || empty($data['reportId'])) {
                throw new Exception('Report ID is required');
            }
            
            $reportId = $data['reportId'];
            
            // Validate required fields
            $this->validateData($data);
            
            // Start transaction
            $this->db->beginTransaction();
            
            // Update main report data
            $this->updateMainReport($reportId, $data);
            
            // Update sector categories if participant data is provided
            if (isset($data['total_attendees']) && $data['total_attendees'] > 0) {
                $this->updateSectorCategories($reportId, $data);
            }
            
            // Update timestamp
            $this->updateTimestamp($reportId);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'Report updated successfully',
                'reportId' => $reportId
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function validateData($data) {
        $requiredFields = ['course_title', 'training_date', 'venue'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                throw new Exception("Required field '{$field}' is missing or empty");
            }
        }
        
        // Validate date format
        if (isset($data['training_date'])) {
            $date = DateTime::createFromFormat('Y-m-d', $data['training_date']);
            if (!$date || $date->format('Y-m-d') !== $data['training_date']) {
                throw new Exception('Invalid training date format');
            }
        }
        
        // Validate participant numbers
        if (isset($data['total_attendees']) || isset($data['male_attendees']) || isset($data['female_attendees'])) {
            $total = (int)($data['total_attendees'] ?? 0);
            $male = (int)($data['male_attendees'] ?? 0);
            $female = (int)($data['female_attendees'] ?? 0);
            
            if ($total > 0 && ($male + $female) !== $total) {
                throw new Exception('Male + Female attendees must equal Total attendees');
            }
            
            if ($male < 0 || $female < 0 || $total < 0) {
                throw new Exception('Attendee numbers cannot be negative');
            }
        }
    }
    
    private function updateMainReport($reportId, $data) {
        $updateFields = [];
        $updateValues = [];
        
        // Define all updatable fields
        $fieldMapping = [
            'course_title' => 'course_title',
            'course_code' => 'course_code',
            'training_date' => 'training_date',
            'training_time' => 'training_time',
            'duration' => 'duration',
            'venue' => 'venue',
            'resource_person' => 'resource_person',
            'platform_used' => 'platform_used',
            'mode' => 'mode',
            'target_participants' => 'target_participants',
            'total_attendees' => 'total_attendees',
            'male_attendees' => 'male_attendees',
            'female_attendees' => 'female_attendees',
            'rationale' => 'rationale',
            'objectives' => 'objectives',
            'topics_covered' => 'topics_covered',
            'issues_concerns' => 'issues_concerns',
            'recommendations' => 'recommendations',
            'action_plans' => 'action_plans',
            'prepared_by' => 'prepared_by',
            'prepared_by_designation' => 'prepared_by_designation',
            'approved_by' => 'approved_by',
            'approved_by_designation' => 'approved_by_designation'
        ];
        
        foreach ($fieldMapping as $dataKey => $dbField) {
            if (array_key_exists($dataKey, $data)) {
                $updateFields[] = "$dbField = ?";
                $updateValues[] = $data[$dataKey];
            }
        }
        
        if (!empty($updateFields)) {
            $updateValues[] = $reportId;
            $query = "UPDATE activity_reports SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute($updateValues);
        }
    }
    
    private function updateSectorCategories($reportId, $data) {
        $totalAttendees = (int)($data['total_attendees'] ?? 0);
        $maleAttendees = (int)($data['male_attendees'] ?? 0);
        $femaleAttendees = (int)($data['female_attendees'] ?? 0);
        
        if ($totalAttendees <= 0) {
            return;
        }
        
        // Get existing sector categories
        $query = "SELECT * FROM sector_categories WHERE report_id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
        $sectors = $stmt->fetchAll();
        
        if (empty($sectors)) {
            // Create default sector categories
            $defaultSectors = ['NGA', 'LGU', 'SUC', 'Others'];
            foreach ($defaultSectors as $sector) {
                $insertQuery = "INSERT INTO sector_categories (report_id, sector_name, total_count, male_count, female_count) VALUES (?, ?, 0, 0, 0)";
                $insertStmt = $this->db->prepare($insertQuery);
                $insertStmt->execute([$reportId, $sector]);
            }
        }
        
        // Update the first sector with the total counts (assuming most participants are from primary sector)
        $updateQuery = "UPDATE sector_categories SET total_count = ?, male_count = ?, female_count = ? WHERE report_id = ? ORDER BY id LIMIT 1";
        $updateStmt = $this->db->prepare($updateQuery);
        $updateStmt->execute([$totalAttendees, $maleAttendees, $femaleAttendees, $reportId]);
    }
    
    private function updateTimestamp($reportId) {
        $query = "UPDATE activity_reports SET updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$reportId]);
    }
    
    public function getReportData($reportId) {
        try {
            $query = "SELECT * FROM activity_reports WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$reportId]);
            $report = $stmt->fetch();
            
            if (!$report) {
                throw new Exception('Report not found');
            }
            
            return [
                'success' => true,
                'report' => $report
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
        exit;
    }
    
    $updater = new ReportUpdater();
    $result = $updater->updateReport($input);
    echo json_encode($result);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['id'])) {
    $updater = new ReportUpdater();
    $result = $updater->getReportData($_GET['id']);
    echo json_encode($result);
    
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?>
