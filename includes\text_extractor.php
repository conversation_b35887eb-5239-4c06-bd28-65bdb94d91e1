<?php
/**
 * Advanced Text Extraction for Multiple File Formats
 */

class TextExtractor {
    
    public function extractText($filePath, $fileType) {
        switch ($fileType) {
            case 'application/pdf':
                return $this->extractFromPDF($filePath);
            
            case 'application/msword':
                return $this->extractFromDOC($filePath);
            
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return $this->extractFromDOCX($filePath);
            
            case 'text/plain':
                return $this->extractFromTXT($filePath);
            
            default:
                if (strpos($fileType, 'image/') === 0) {
                    return $this->extractFromImage($filePath);
                }
                return '';
        }
    }

    private function extractFromPDF($filePath) {
        $text = '';
        
        // Method 1: Try pdftotext command line tool
        if ($this->commandExists('pdftotext')) {
            $escapedPath = escapeshellarg($filePath);
            $output = shell_exec("pdftotext $escapedPath -");
            if ($output && trim($output)) {
                return $output;
            }
        }
        
        // Method 2: Try using PHP PDF parser libraries if available
        if (class_exists('Smalot\PdfParser\Parser')) {
            try {
                $parser = new \Smalot\PdfParser\Parser();
                $pdf = $parser->parseFile($filePath);
                $text = $pdf->getText();
                if ($text && trim($text)) {
                    return $text;
                }
            } catch (Exception $e) {
                error_log("PDF parsing error: " . $e->getMessage());
            }
        }
        
        // Method 3: Basic PDF text extraction (limited effectiveness)
        $content = file_get_contents($filePath);
        if ($content) {
            // Extract text between parentheses (basic PDF text extraction)
            if (preg_match_all('/\((.*?)\)/', $content, $matches)) {
                $text = implode(' ', $matches[1]);
            }
            
            // Also try to extract text between BT and ET markers
            if (preg_match_all('/BT\s*(.*?)\s*ET/s', $content, $matches)) {
                foreach ($matches[1] as $match) {
                    if (preg_match_all('/\((.*?)\)/', $match, $textMatches)) {
                        $text .= ' ' . implode(' ', $textMatches[1]);
                    }
                }
            }
        }
        
        return $this->cleanText($text);
    }

    private function extractFromDOC($filePath) {
        // Method 1: Try antiword command line tool
        if ($this->commandExists('antiword')) {
            $escapedPath = escapeshellarg($filePath);
            $output = shell_exec("antiword $escapedPath");
            if ($output && trim($output)) {
                return $this->cleanText($output);
            }
        }
        
        // Method 2: Try catdoc if available
        if ($this->commandExists('catdoc')) {
            $escapedPath = escapeshellarg($filePath);
            $output = shell_exec("catdoc $escapedPath");
            if ($output && trim($output)) {
                return $this->cleanText($output);
            }
        }
        
        // Method 3: Basic binary extraction (very limited)
        $content = file_get_contents($filePath);
        if ($content) {
            // Remove binary characters and extract readable text
            $text = preg_replace('/[^\x20-\x7E\x0A\x0D]/', '', $content);
            $text = preg_replace('/\s+/', ' ', $text);
            return $this->cleanText($text);
        }
        
        return '';
    }

    private function extractFromDOCX($filePath) {
        $text = '';
        
        try {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                // Extract main document content
                $xml = $zip->getFromName('word/document.xml');
                if ($xml) {
                    $text .= $this->extractTextFromXML($xml);
                }
                
                // Extract headers
                for ($i = 1; $i <= 3; $i++) {
                    $headerXml = $zip->getFromName("word/header$i.xml");
                    if ($headerXml) {
                        $text .= "\n" . $this->extractTextFromXML($headerXml);
                    }
                }
                
                // Extract footers
                for ($i = 1; $i <= 3; $i++) {
                    $footerXml = $zip->getFromName("word/footer$i.xml");
                    if ($footerXml) {
                        $text .= "\n" . $this->extractTextFromXML($footerXml);
                    }
                }
                
                $zip->close();
            }
        } catch (Exception $e) {
            error_log("DOCX extraction error: " . $e->getMessage());
        }
        
        return $this->cleanText($text);
    }

    private function extractTextFromXML($xml) {
        // Load XML and extract text content
        $dom = new DOMDocument();
        $dom->loadXML($xml);
        
        // Remove all XML tags and get plain text
        $text = $dom->textContent;
        
        // Clean up whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        return trim($text);
    }

    private function extractFromTXT($filePath) {
        $content = file_get_contents($filePath);
        return $content ? $this->cleanText($content) : '';
    }

    private function extractFromImage($filePath) {
        $metadata = [];
        
        // Extract basic image information
        $imageInfo = getimagesize($filePath);
        if ($imageInfo) {
            $metadata[] = "Image dimensions: {$imageInfo[0]}x{$imageInfo[1]}";
            $metadata[] = "Image type: " . image_type_to_mime_type($imageInfo[2]);
        }
        
        // Extract EXIF data if available
        if (function_exists('exif_read_data') && in_array(strtolower(pathinfo($filePath, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'tiff'])) {
            try {
                $exif = exif_read_data($filePath);
                if ($exif) {
                    if (isset($exif['DateTime'])) {
                        $metadata[] = "Date taken: " . $exif['DateTime'];
                    }
                    if (isset($exif['Make']) && isset($exif['Model'])) {
                        $metadata[] = "Camera: " . $exif['Make'] . " " . $exif['Model'];
                    }
                }
            } catch (Exception $e) {
                error_log("EXIF extraction error: " . $e->getMessage());
            }
        }
        
        // Get filename without extension as potential description
        $filename = pathinfo($filePath, PATHINFO_FILENAME);
        $metadata[] = "Filename: " . $filename;
        
        return implode("\n", $metadata);
    }

    private function commandExists($command) {
        $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ["pipe", "r"], // stdin
                1 => ["pipe", "w"], // stdout
                2 => ["pipe", "w"], // stderr
            ],
            $pipes
        );
        
        if ($process !== false) {
            $stdout = stream_get_contents($pipes[1]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            proc_close($process);
            
            return !empty(trim($stdout));
        }
        
        return false;
    }

    private function cleanText($text) {
        if (!$text) return '';

        // Remove excessive whitespace
        $text = preg_replace('/\s+/', ' ', $text);

        // Remove control characters except newlines and tabs
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Normalize line endings
        $text = str_replace(["\r\n", "\r"], "\n", $text);

        // Remove excessive newlines
        $text = preg_replace('/\n{3,}/', "\n\n", $text);

        return trim($text);
    }
}
?>
?>
